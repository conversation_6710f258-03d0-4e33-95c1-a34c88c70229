import React, { useState } from 'react';
import { client } from '../api/client';

const MediaUploadTest: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    setResult(null);
    setError('');
    
    try {
      console.log('📤 开始上传文件:', file.name, file.type, file.size);
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('courseId', '1'); // 测试用的课程ID

      // 先测试基本的上传功能
      const response = await client.post('/media/upload', formData);

      console.log('✅ 上传成功:', response.data);
      setResult(response.data);
      
    } catch (error: any) {
      console.error('❌ 上传失败:', error);
      const errorMessage = error.response?.data?.error || error.message || '未知错误';
      setError(errorMessage);
    } finally {
      setUploading(false);
      // 清空input值
      event.target.value = '';
    }
  };

  const testPing = async () => {
    try {
      const response = await client.get('/ping');
      console.log('Ping 测试成功:', response.data);
      alert('Ping 测试成功: ' + response.data);
    } catch (error: any) {
      console.error('Ping 测试失败:', error);
      alert('Ping 测试失败: ' + (error.message || '未知错误'));
    }
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">媒体上传测试</h2>
      
      {/* Ping 测试按钮 */}
      <div className="mb-4">
        <button
          onClick={testPing}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          测试 API 连接
        </button>
      </div>

      {/* 文件上传 */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          选择视频文件
        </label>
        <input
          type="file"
          accept="video/*"
          onChange={handleFileUpload}
          disabled={uploading}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
      </div>

      {/* 上传状态 */}
      {uploading && (
        <div className="mb-4 p-3 bg-blue-100 text-blue-700 rounded">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
            上传中...
          </div>
        </div>
      )}

      {/* 成功结果 */}
      {result && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded">
          <h3 className="font-semibold mb-2">上传成功！</h3>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
          <h3 className="font-semibold mb-2">上传失败</h3>
          <p className="text-sm">{error}</p>
        </div>
      )}

      {/* 使用说明 */}
      <div className="text-xs text-gray-500">
        <p>• 请选择视频文件进行测试</p>
        <p>• 支持格式: MP4, AVI, MOV, WMV, FLV, WebM, MKV</p>
        <p>• 最大文件大小: 500MB</p>
      </div>
    </div>
  );
};

export default MediaUploadTest;
