// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { client } from '../api/client';
// import ApiTest from '../components/ApiTest';
import { 
  departments, 
  subjects, 
  teachers, 
  courses, 
  semesters, 
  timeSlots, 
  courseAdvisors,
  type Department,
  type Subject,
  type Teacher,
  type Course,
  type TimeSlot
} from '../data/mockData';

// 定义真实课程数据类型
interface RealCourse {
  id: number;
  title: string;
  description: string;
  teacherName: string;
  createdAt: string;
}

// 定义上传文件类型
interface UploadedFile {
  mediaId: string;
  fileName: string;
  uploadTime: string;
  courseId?: number;    // 关联的课程ID
  courseName?: string;  // 关联的课程名称
}

const App: React.FC = () => {
const navigate = useNavigate();
const [username, setUsername] = useState('');
const [password, setPassword] = useState('');
const [showPassword, setShowPassword] = useState(false);
const [rememberMe, setRememberMe] = useState(false);
const [pingMsg, setPingMsg] = useState('');
const [selectedTimeframe, setSelectedTimeframe] = useState('');
const [dropdownOpen, setDropdownOpen] = useState(false);
const [isSubmitting, setIsSubmitting] = useState(false);
const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
const [showCourseSelection, setShowCourseSelection] = useState(false);
const [selectedDepartment, setSelectedDepartment] = useState('');
const [selectedSubject, setSelectedSubject] = useState('');
const [selectedTeacher, setSelectedTeacher] = useState('');
const [selectedSemester, setSelectedSemester] = useState('');
const [selectedTimeSlot, setSelectedTimeSlot] = useState('');
const [searchTerm, setSearchTerm] = useState('');

// 添加真实课程数据状态
const [realCourses, setRealCourses] = useState<RealCourse[]>([]);
const [coursesLoading, setCoursesLoading] = useState(false);

// 添加视频上传相关状态
const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
const [uploading, setUploading] = useState(false);

// 添加课程选择状态
const [selectedCourse, setSelectedCourse] = useState<RealCourse | null>(null);

// 添加创建任务的加载状态
const [creatingTask, setCreatingTask] = useState(false);

// Axios ping测试
useEffect(() => {
  client.get('/ping')
    .then(r => {
      console.log('Axios response:', r);
      setPingMsg(r.data);
    })
    .catch(err => {
      console.error('Axios error:', err);
      setPingMsg('Error: ' + err.message);
    });
}, []);

// 获取真实课程数据
useEffect(() => {
  const fetchCourses = async () => {
    setCoursesLoading(true);
    try {
      const response = await client.get('/courses');
      setRealCourses(response.data);
      console.log('获取到课程数据:', response.data);
    } catch (error) {
      console.error('获取课程数据失败:', error);
    } finally {
      setCoursesLoading(false);
    }
  };
  
  fetchCourses();
}, []);

// 视频上传处理函数
const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
  const file = event.target.files?.[0];
  if (!file) return;

  // 检查是否已选择课程
  if (!selectedCourse) {
    alert('请先选择要评价的课程！');
    return;
  }

  setUploading(true);
  
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('courseId', selectedCourse.id.toString()); // 传递courseId

    const response = await client.post('/media/upload', formData);

    if (response.data.success) {
      const newFile: UploadedFile = {
        mediaId: response.data.mediaId,
        fileName: response.data.fileName,
        uploadTime: new Date().toLocaleString(),
        courseId: selectedCourse.id,        // 新增：记录关联的课程
        courseName: selectedCourse.title    // 新增：课程名称
      };

      // 追加到state
      setUploadedFiles(prev => [...prev, newFile]);

      console.log('📁 上传成功，文件信息:', newFile);
      console.log('💾 保存路径:', response.data.savedPath);
      console.log('📚 关联课程:', selectedCourse.title);
      
      alert(`上传成功！\n课程: ${selectedCourse.title}\n文件: ${newFile.fileName}\nMedia ID: ${newFile.mediaId}`);
    }
  } catch (error: any) {
    console.error('❌ 上传失败:', error);
    alert(`上传失败: ${error.response?.data?.error || error.message}`);
  } finally {
    setUploading(false);
    // 清空input值，允许重复上传同一文件
    event.target.value = '';
  }
};

// 基于选择的院系过滤科目
const filteredSubjects = selectedDepartment 
  ? subjects.filter(subject => subject.departmentId === selectedDepartment)
  : subjects;

// 基于选择的院系过滤教师
const filteredTeachers = selectedDepartment 
  ? teachers.filter(teacher => teacher.departmentId === selectedDepartment)
  : teachers;

// 基于各种条件过滤课程
const filteredCourses = courses.filter(course => {
  if (selectedDepartment && course.departmentId !== selectedDepartment) return false;
  if (selectedSubject && course.subjectId !== selectedSubject) return false;
  if (selectedTeacher && course.teacherId !== selectedTeacher) return false;
  if (selectedSemester && course.semester !== selectedSemester) return false;
  if (selectedTimeSlot && course.timeSlotId !== selectedTimeSlot) return false;
  if (searchTerm && !course.name.toLowerCase().includes(searchTerm.toLowerCase())) return false;
  return true;
});

// 过滤真实课程数据
const filteredRealCourses = realCourses.filter(course => {
  const matchesSearch = !searchTerm || 
    course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    course.teacherName.toLowerCase().includes(searchTerm.toLowerCase());
  
  return matchesSearch;
});

// 获取相关数据的辅助函数
const getDepartmentName = (id: string) => departments.find(d => d.id === id)?.name || '';
const getSubjectName = (id: string) => subjects.find(s => s.id === id)?.name || '';
const getTeacherName = (id: string) => teachers.find(t => t.id === id)?.name || '';
const getTimeSlotLabel = (id: string) => timeSlots.find(ts => ts.id === id)?.label || '';
const handleLogin = async (e: React.FormEvent) => {
e.preventDefault();
if (!username || !password || !selectedTimeframe) {
setSubmitStatus('error');
return;
}
setIsSubmitting(true);
setSubmitStatus('idle');
setShowCourseSelection(true);
const formData = new URLSearchParams();
formData.append('username', username);
formData.append('password', password);
formData.append('timeframe', selectedTimeframe);
formData.append('rememberMe', rememberMe.toString());
try {
const response = await fetch('https://readdy.ai/api/form/d1mbu7nlh67ghbd8brj0', {
method: 'POST',
headers: {
'Content-Type': 'application/x-www-form-urlencoded',
},
body: formData,
});
if (response.ok) {
setSubmitStatus('success');
setUsername('');
setPassword('');
setSelectedTimeframe('');
setRememberMe(false);
} else {
setSubmitStatus('error');
}
} catch (error) {
setSubmitStatus('error');
} finally {
setIsSubmitting(false);
}
};
// 使用导入的课程顾问数据
return (
<div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
<div className="absolute w-96 h-96 bg-blue-200 rounded-full filter blur-3xl opacity-20 animate-pulse -top-10 -left-10"></div>
<div className="absolute w-96 h-96 bg-purple-200 rounded-full filter blur-3xl opacity-20 animate-pulse -bottom-10 -right-10"></div>
<div className="w-full max-w-xl bg-white/80 backdrop-blur-lg rounded-3xl shadow-lg p-10 relative z-10 border border-white/20">
<div className="text-center mb-8">
<div className="flex justify-center mb-4">
<div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center transform rotate-12 shadow-lg hover:rotate-0 transition-all duration-300 hover:scale-110">
<i className="fas fa-graduation-cap text-white text-3xl"></i>
</div>
</div>
<h1 className="text-3xl font-bold text-blue-600">AI 课程评估系统</h1>
<p className="text-gray-600 mt-2"></p>
</div>
<form onSubmit={handleLogin} className="space-y-6" data-readdy-form id="d1mbu7nlh67ghbd8brj0">
<div>
<label htmlFor="username" className="block text-gray-700 mb-2">用户名</label>
<div className="relative">
<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
<i className="fas fa-envelope text-gray-400"></i>
</div>
<input
id="username"
type="text"
value={username}
onChange={(e) => setUsername(e.target.value)}
className="w-full pl-10 pr-3 py-3 border-none bg-gray-100/70 backdrop-blur-sm rounded-lg focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-300 hover:bg-gray-100/90"
placeholder="请输入您的用户名/手机号/邮箱"
/>
</div>
</div>
<div>
<label htmlFor="password" className="block text-gray-700 mb-2">密码</label>
<div className="relative">
<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
<i className="fas fa-lock text-gray-400"></i>
</div>
<input
id="password"
type={showPassword ? "text" : "password"}
value={password}
onChange={(e) => setPassword(e.target.value)}
className="w-full pl-10 pr-10 py-3 border-none bg-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm"
placeholder="请输入您的密码"
/>
<div
className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
onClick={() => setShowPassword(!showPassword)}
>
<i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-400`}></i>
</div>
</div>
</div>
<div>
<label htmlFor="timeframe" className="block text-gray-700 mb-2">选择您的课程顾问</label>
<div className="relative">
<button
type="button"
className="w-full pl-3 pr-10 py-3 border-none bg-gray-100 rounded-lg text-left text-sm focus:ring-2 focus:ring-blue-500 cursor-pointer"
onClick={() => setDropdownOpen(!dropdownOpen)}
>
{selectedTimeframe || '请选择课程顾问'}
<div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
<i className={`fas fa-chevron-${dropdownOpen ? 'up' : 'down'} text-gray-400`}></i>
</div>
</button>
{dropdownOpen && (
<div className="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg max-h-60 overflow-auto">
{courseAdvisors.map((option, index) => (
<div
key={index}
className="px-4 py-3 hover:bg-blue-50 cursor-pointer text-sm"
onClick={() => {
setSelectedTimeframe(option);
setDropdownOpen(false);
}}
>
{option}
</div>
))}
</div>
)}
</div>
</div>
<div className="flex items-center justify-between">
<div className="flex items-center">
<input
id="remember-me"
type="checkbox"
checked={rememberMe}
onChange={() => setRememberMe(!rememberMe)}
className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
/>
<label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 cursor-pointer">
记住我
</label>
</div>
<div className="text-sm">
<a href="#" className="font-medium text-blue-600 hover:text-blue-500">
忘记密码?
</a>
</div>
</div>
<button
type="submit"
disabled={isSubmitting}
className={`w-full py-3 px-4 text-white font-medium rounded-lg transition-all duration-300 !rounded-button whitespace-nowrap bg-gradient-to-r from-blue-500 to-indigo-600 hover:shadow-lg hover:scale-[1.02] transform ${
isSubmitting
? 'opacity-75 cursor-not-allowed'
: 'cursor-pointer hover:from-blue-600 hover:to-indigo-700'
}`}
>
{isSubmitting ? (
<span className="flex items-center justify-center">
<i className="fas fa-circle-notch fa-spin mr-2"></i>
提交中...
</span>
) : '登录'}
</button>
{submitStatus === 'success' && (
<div className="mt-4 text-green-600 text-sm text-center">
<i className="fas fa-check-circle mr-1"></i>
提交成功
</div>
)}
{submitStatus === 'error' && (
<div className="mt-4 text-red-600 text-sm text-center">
<i className="fas fa-exclamation-circle mr-1"></i>
提交失败，请检查输入并重试
</div>
)}
</form>
<div className="mt-8 relative">
  <div className="absolute inset-0 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-2xl blur"></div>
  <div className="relative bg-white/50 backdrop-blur-sm p-6 rounded-2xl border border-white/40 shadow-lg transform hover:scale-105 transition-all duration-300">
    <div className="flex items-center justify-center space-x-4">
      <div className="text-base">
        <span className="text-gray-600">还没有账号? </span>
        <a href="#" className="inline-flex items-center font-medium bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent hover:from-blue-700 hover:to-indigo-700 group">
          立即注册
          <i className="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
        </a>
      </div>
    </div>
  </div>
</div>
<div className="mt-8 text-center text-xs text-gray-500">
<i className="fas fa-shield-alt mr-1"></i> 您的信息将被安全加密保护
</div>
{showCourseSelection && (
<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
<div className="bg-white rounded-2xl p-8 w-[800px] max-h-[80vh] overflow-y-auto">
<div className="flex justify-between items-center mb-6">
<h2 className="text-2xl font-bold text-gray-800">选择课程评价</h2>
<button onClick={() => setShowCourseSelection(false)} className="text-gray-500 hover:text-gray-700">
<i className="fas fa-times text-xl"></i>
</button>
</div>

{/* 视频上传区域 */}
<div className="mb-6 p-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
<h3 className="text-md font-medium text-gray-700 mb-3">📹 视频上传</h3>

{/* 显示当前选中的课程 */}
{selectedCourse ? (
<div className="mb-4 p-3 bg-blue-100 rounded-lg border border-blue-300">
  <p className="text-sm text-blue-800">
    <i className="fas fa-check-circle mr-2"></i>
    已选择课程：<strong>{selectedCourse.title}</strong>
  </p>
  <p className="text-xs text-blue-600">教师：{selectedCourse.teacherName}</p>
</div>
) : (
<div className="mb-4 p-3 bg-yellow-100 rounded-lg border border-yellow-300">
  <p className="text-sm text-yellow-800">
    <i className="fas fa-info-circle mr-2"></i>
    请先在下方选择要评价的课程
  </p>
</div>
)}

<div className="flex items-center gap-4">
<label className="cursor-pointer">
<input
  type="file"
  accept="video/*"
  onChange={handleFileUpload}
  disabled={uploading || !selectedCourse}
  className="hidden"
/>
<span className={`px-4 py-2 rounded-lg text-white font-medium ${
  uploading || !selectedCourse
    ? 'bg-gray-400 cursor-not-allowed' 
    : 'bg-blue-500 hover:bg-blue-600 cursor-pointer'
}`}>
  {uploading ? '上传中...' : '选择视频文件'}
</span>
</label>
{uploading && (
<div className="flex items-center text-blue-600">
  <i className="fas fa-spinner fa-spin mr-2"></i>
  <span>处理中，请稍候...</span>
</div>
)}
</div>
<p className="text-sm text-gray-500 mt-2">支持格式：MP4, AVI, MOV, WMV, FLV, WebM, MKV (最大500MB)</p>

{/* 已上传文件列表 */}
{uploadedFiles.length > 0 && (
<div className="mt-4">
  <h4 className="text-sm font-medium text-gray-600 mb-2">已上传的文件:</h4>
  <div className="space-y-2">
    {uploadedFiles.map((file, index) => (
      <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
        <div className="flex items-center">
          <i className="fas fa-video text-blue-500 mr-3"></i>
          <div>
            <p className="font-medium text-gray-800">{file.fileName}</p>
            <p className="text-xs text-gray-500">Media ID: {file.mediaId} | {file.uploadTime}</p>
          </div>
        </div>
        <span className="text-green-600 text-sm">✅ 已保存</span>
      </div>
    ))}
  </div>
</div>
)}
</div>
<div className="space-y-6">
<div className="flex gap-4 flex-wrap">
<div className="flex-1 min-w-[200px]">
<label className="block text-gray-700 mb-2">院系</label>
<select
className="w-full p-3 bg-gray-100 rounded-lg border-none"
value={selectedDepartment}
onChange={(e) => {
setSelectedDepartment(e.target.value);
// 清空相关的选择
setSelectedSubject('');
setSelectedTeacher('');
}}
>
<option value="">请选择院系</option>
{departments.map((department) => (
<option key={department.id} value={department.id}>
{department.name}
</option>
))}
</select>
</div>
<div className="flex-1 min-w-[200px]">
<label className="block text-gray-700 mb-2">科目</label>
<select
className="w-full p-3 bg-gray-100 rounded-lg border-none"
value={selectedSubject}
onChange={(e) => setSelectedSubject(e.target.value)}
>
<option value="">请选择科目</option>
{filteredSubjects.map((subject) => (
<option key={subject.id} value={subject.id}>
{subject.name}
</option>
))}
</select>
</div>
<div className="flex-1 min-w-[200px]">
<label className="block text-gray-700 mb-2">授课教师</label>
<select
className="w-full p-3 bg-gray-100 rounded-lg border-none"
value={selectedTeacher}
onChange={(e) => setSelectedTeacher(e.target.value)}
>
<option value="">请选择教师</option>
{filteredTeachers.map((teacher) => (
<option key={teacher.id} value={teacher.id}>
{teacher.name} ({teacher.title})
</option>
))}
</select>
</div>
<div className="flex-1 min-w-[200px]">
<label className="block text-gray-700 mb-2">学期</label>
<select
className="w-full p-3 bg-gray-100 rounded-lg border-none"
value={selectedSemester}
onChange={(e) => setSelectedSemester(e.target.value)}
>
<option value="">请选择学期</option>
{semesters.map((semester) => (
<option key={semester} value={semester}>
{semester}
</option>
))}
</select>
</div>
<div className="flex-1 min-w-[200px]">
<label className="block text-gray-700 mb-2">上课时间</label>
<select
className="w-full p-3 bg-gray-100 rounded-lg border-none"
value={selectedTimeSlot}
onChange={(e) => setSelectedTimeSlot(e.target.value)}
>
<option value="">请选择上课时间</option>
{timeSlots.map((timeSlot) => (
<option key={timeSlot.id} value={timeSlot.id}>
{timeSlot.label}
</option>
))}
</select>
</div>
</div>
<div className="relative">
<input
type="text"
placeholder="快速搜索课程"
value={searchTerm}
onChange={(e) => setSearchTerm(e.target.value)}
className="w-full pl-10 pr-3 py-3 bg-gray-100 rounded-lg border-none focus:ring-2 focus:ring-blue-500"
/>
<i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
</div>

{/* 真实课程数据展示 */}
<div className="mt-6">
<h3 className="text-lg font-medium text-gray-800 mb-4">
可评估课程 ({realCourses.length}门课程)
{coursesLoading && <span className="text-sm text-gray-500 ml-2">加载中...</span>}
</h3>
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
{coursesLoading ? (
<div className="col-span-2 text-center py-8 text-gray-500">
<i className="fas fa-spinner fa-spin text-3xl mb-2"></i>
<p>正在加载课程数据...</p>
</div>
) : filteredRealCourses.length > 0 ? (
filteredRealCourses.map((course) => (
<div 
  key={course.id} 
  className={`p-4 rounded-xl border-2 cursor-pointer transition-colors hover:shadow-md ${
    selectedCourse?.id === course.id 
      ? 'bg-blue-100 border-blue-500 shadow-md' // 选中状态
      : 'bg-blue-50 border-blue-200 hover:bg-blue-100' // 普通状态
  }`}
  onClick={() => {
    setSelectedCourse(course);
    console.log('选中课程:', course);
  }}
>
<div className="flex justify-between items-start">
<div className="flex-1">
<h3 className="font-medium text-gray-800">{course.title}</h3>
<p className="text-sm text-gray-600 mt-1">
教师: {course.teacherName}
</p>
<p className="text-xs text-gray-500 mt-1">
{course.description}
</p>
<p className="text-xs text-gray-500">
创建时间: {new Date(course.createdAt).toLocaleDateString()}
</p>
</div>
<div className="flex flex-col items-end space-y-1">
{selectedCourse?.id === course.id && (
<span className="text-green-600 text-sm font-medium bg-green-100 px-2 py-1 rounded">
✓ 已选中
</span>
)}
<span className="text-blue-600 text-sm font-medium bg-blue-100 px-2 py-1 rounded">
真实数据
</span>
</div>
</div>
</div>
))
) : (
<div className="col-span-2 text-center py-8 text-gray-500">
<i className="fas fa-database text-3xl mb-2"></i>
<p>未找到符合条件的课程</p>
<p className="text-sm">请尝试调整搜索条件</p>
</div>
)}
</div>
</div>

{/* 
  暂时隐藏模拟数据展示，专注于真实业务数据
  如需要演示对比，可以取消下面的注释
*/}
{/* Mock数据展示（保留原有逻辑） */}
{/*
<div className="mt-8">
<h3 className="text-lg font-medium text-gray-800 mb-4">模拟课程数据 (演示用)</h3>
<div className="grid grid-cols-2 gap-4">
{filteredCourses.length > 0 ? (
filteredCourses.map((course) => (
<div key={course.id} className="bg-gray-50 p-4 rounded-xl hover:bg-gray-100 cursor-pointer transition-colors">
<div className="flex justify-between items-start">
<div className="flex-1">
<h3 className="font-medium text-gray-800">{course.name}</h3>
<p className="text-sm text-gray-600 mt-1">
{getTeacherName(course.teacherId)} | {course.semester}
</p>
<p className="text-xs text-gray-500 mt-1">
{getDepartmentName(course.departmentId)} - {getSubjectName(course.subjectId)}
</p>
<p className="text-xs text-gray-500">
{getTimeSlotLabel(course.timeSlotId)}
</p>
</div>
<span className="text-blue-600 text-sm font-medium">
评分: {course.rating}
</span>
</div>
</div>
))
) : (
<div className="col-span-2 text-center py-8 text-gray-500">
<i className="fas fa-search text-3xl mb-2"></i>
<p>未找到符合条件的课程</p>
<p className="text-sm">请尝试调整筛选条件</p>
</div>
)}
</div>
</div>
*/}
<div className="mt-6 flex justify-end">
<button 
  className="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg mr-3 hover:bg-gray-300 !rounded-button whitespace-nowrap"
  onClick={() => setShowCourseSelection(false)}
>
  取消
</button>
<button
  className={`px-6 py-2 rounded-lg !rounded-button whitespace-nowrap flex items-center justify-center ${
    selectedCourse && uploadedFiles.length > 0
      ? 'bg-blue-500 text-white hover:bg-blue-600 cursor-pointer'
      : 'bg-gray-400 text-gray-200 cursor-not-allowed'
  }`}
  disabled={!selectedCourse || uploadedFiles.length === 0 || creatingTask}
  onClick={async () => {
    if (selectedCourse && uploadedFiles.length > 0 && !creatingTask) {
      console.log(`开始评价课程: ${selectedCourse.title} (ID: ${selectedCourse.id})`);

      setCreatingTask(true);

      try {
        console.log('📤 发送创建任务请求...');

        // 调用创建评估任务接口
        const response = await client.post('/tasks', {
          courseId: selectedCourse.id
        });

        console.log('📥 收到响应:', response.data);

        if (response.data.taskId) {
          console.log('✅ 评估任务创建成功:', response.data);
          setShowCourseSelection(false);
          // 传递taskId和courseId到评估页面
          navigate(`/style-demo?courseId=${selectedCourse.id}&taskId=${response.data.taskId}`);
        } else {
          throw new Error('响应中缺少 taskId');
        }
      } catch (error: any) {
        console.error('❌ 创建评估任务失败:', error);

        let errorMessage = '创建评估任务失败';
        if (error.code === 'ECONNABORTED') {
          errorMessage = '请求超时，请检查网络连接后重试';
        } else if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.message) {
          errorMessage = error.message;
        }

        alert(errorMessage);
      } finally {
        setCreatingTask(false);
      }
    }
}}
>
{!selectedCourse ? (
  <>
    <i className="fas fa-exclamation-circle mr-2"></i>
    <span>请选择课程</span>
  </>
) : uploadedFiles.length === 0 ? (
  <>
    <i className="fas fa-upload mr-2"></i>
    <span>请上传视频</span>
  </>
) : creatingTask ? (
  <>
    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
    <span>创建任务中...</span>
  </>
) : (
  <>
    <i className="fas fa-play mr-2"></i>
    <span>开始评价</span>
  </>
)}
</button>
</div>
</div>
</div>
</div>
)}
{/* API测试区域 - 简化版本 */}
<div className="fixed bottom-4 right-4 z-50">
<button
onClick={async () => {
try {
const response = await fetch('/api/v1/ping');
const result = await response.text();
alert(`后端响应: ${result}`);
} catch (error) {
alert(`连接失败: ${error}`);
}
}}
className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 shadow-lg"
>
测试API
</button>
{pingMsg && (
<p className="mt-4 text-sm text-green-600">
Axios测试: {pingMsg}
</p>
)}
</div>
</div>
</div>
);
};

export default App;
