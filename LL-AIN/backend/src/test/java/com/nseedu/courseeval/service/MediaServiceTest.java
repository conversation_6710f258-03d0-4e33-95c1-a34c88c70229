package com.nseedu.courseeval.service;

import com.nseedu.courseeval.entity.CourseMedia;
import com.nseedu.courseeval.entity.MediaType;
import com.nseedu.courseeval.entity.StorageType;
import com.nseedu.courseeval.repository.CourseMediaRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MediaServiceTest {

    @Mock
    private CourseMediaRepository courseMediaRepository;

    @InjectMocks
    private MediaService mediaService;

    private CourseMedia testMedia;

    @BeforeEach
    void setUp() {
        // 设置测试用的上传目录
        ReflectionTestUtils.setField(mediaService, "uploadDir", "/tmp/test-uploads");
        
        // 创建测试媒体对象
        testMedia = new CourseMedia();
        testMedia.setId(1L);
        testMedia.setCourseId(100L);
        testMedia.setMediaType(MediaType.VIDEO);
        testMedia.setStorageType(StorageType.LOCAL);
        testMedia.setFilePath("/tmp/test-uploads/20241218_120000_abc12345_test.mp4");
        testMedia.setSizeBytes(1024000L);
        testMedia.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void testGetCourseMedia() {
        // Given
        Long courseId = 100L;
        List<CourseMedia> mediaList = Arrays.asList(testMedia);
        when(courseMediaRepository.findByCourseIdOrderByCreatedAt(courseId)).thenReturn(mediaList);

        // When
        List<Map<String, Object>> result = mediaService.getCourseMedia(courseId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Map<String, Object> mediaInfo = result.get(0);
        assertEquals(1L, mediaInfo.get("id"));
        assertEquals("VIDEO", mediaInfo.get("mediaType"));
        assertEquals("LOCAL", mediaInfo.get("storageType"));
        assertEquals(1024000L, mediaInfo.get("sizeBytes"));
        assertEquals("test.mp4", mediaInfo.get("originalFileName"));
        
        verify(courseMediaRepository).findByCourseIdOrderByCreatedAt(courseId);
    }

    @Test
    void testGetMediaInfo() {
        // Given
        Long mediaId = 1L;
        when(courseMediaRepository.findById(mediaId)).thenReturn(Optional.of(testMedia));

        // When
        Optional<Map<String, Object>> result = mediaService.getMediaInfo(mediaId);

        // Then
        assertTrue(result.isPresent());
        
        Map<String, Object> mediaInfo = result.get();
        assertEquals(1L, mediaInfo.get("id"));
        assertEquals(100L, mediaInfo.get("courseId"));
        assertEquals("VIDEO", mediaInfo.get("mediaType"));
        assertEquals("test.mp4", mediaInfo.get("originalFileName"));
        
        verify(courseMediaRepository).findById(mediaId);
    }

    @Test
    void testGetMediaInfoNotFound() {
        // Given
        Long mediaId = 999L;
        when(courseMediaRepository.findById(mediaId)).thenReturn(Optional.empty());

        // When
        Optional<Map<String, Object>> result = mediaService.getMediaInfo(mediaId);

        // Then
        assertFalse(result.isPresent());
        verify(courseMediaRepository).findById(mediaId);
    }

    @Test
    void testDeleteMediaNotFound() throws Exception {
        // Given
        Long mediaId = 999L;
        when(courseMediaRepository.findById(mediaId)).thenReturn(Optional.empty());

        // When
        boolean result = mediaService.deleteMedia(mediaId);

        // Then
        assertFalse(result);
        verify(courseMediaRepository).findById(mediaId);
        verify(courseMediaRepository, never()).delete(any());
    }

    @Test
    void testGetCourseMediaStats() {
        // Given
        Long courseId = 100L;
        CourseMedia media1 = new CourseMedia();
        media1.setMediaType(MediaType.VIDEO);
        media1.setStorageType(StorageType.LOCAL);
        media1.setSizeBytes(1024000L);
        media1.setCreatedAt(LocalDateTime.now());

        CourseMedia media2 = new CourseMedia();
        media2.setMediaType(MediaType.IMAGE);
        media2.setStorageType(StorageType.LOCAL);
        media2.setSizeBytes(512000L);
        media2.setCreatedAt(LocalDateTime.now().minusHours(1));

        List<CourseMedia> mediaList = Arrays.asList(media1, media2);
        when(courseMediaRepository.findByCourseIdOrderByCreatedAt(courseId)).thenReturn(mediaList);

        // When
        Map<String, Object> stats = mediaService.getCourseMediaStats(courseId);

        // Then
        assertNotNull(stats);
        assertEquals(2, stats.get("totalCount"));
        assertEquals(1536000L, stats.get("totalSizeBytes"));
        
        @SuppressWarnings("unchecked")
        Map<String, Long> typeStats = (Map<String, Long>) stats.get("typeStats");
        assertEquals(1L, typeStats.get("VIDEO"));
        assertEquals(1L, typeStats.get("IMAGE"));
        
        verify(courseMediaRepository).findByCourseIdOrderByCreatedAt(courseId);
    }

    @Test
    void testGetCourseMediaByType() {
        // Given
        Long courseId = 100L;
        MediaType mediaType = MediaType.VIDEO;
        List<CourseMedia> mediaList = Arrays.asList(testMedia);
        when(courseMediaRepository.findByCourseIdAndMediaType(courseId, mediaType)).thenReturn(mediaList);

        // When
        List<Map<String, Object>> result = mediaService.getCourseMediaByType(courseId, mediaType);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Map<String, Object> mediaInfo = result.get(0);
        assertEquals("VIDEO", mediaInfo.get("mediaType"));
        assertEquals("test.mp4", mediaInfo.get("originalFileName"));
        
        verify(courseMediaRepository).findByCourseIdAndMediaType(courseId, mediaType);
    }

    @Test
    void testBatchDeleteMedia() {
        // Given
        List<Long> mediaIds = Arrays.asList(1L, 2L, 999L); // 999L 不存在
        
        when(courseMediaRepository.findById(1L)).thenReturn(Optional.of(testMedia));
        
        CourseMedia media2 = new CourseMedia();
        media2.setId(2L);
        media2.setFilePath("/tmp/test-uploads/test2.mp4");
        when(courseMediaRepository.findById(2L)).thenReturn(Optional.of(media2));
        
        when(courseMediaRepository.findById(999L)).thenReturn(Optional.empty());

        // When
        Map<String, Object> result = mediaService.batchDeleteMedia(mediaIds);

        // Then
        assertNotNull(result);
        assertEquals(2, result.get("successCount"));
        assertEquals(1, result.get("failedCount"));
        
        @SuppressWarnings("unchecked")
        List<Long> successIds = (List<Long>) result.get("successIds");
        assertTrue(successIds.contains(1L));
        assertTrue(successIds.contains(2L));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> failedItems = (List<Map<String, Object>>) result.get("failedItems");
        assertEquals(1, failedItems.size());
        assertEquals(999L, failedItems.get(0).get("mediaId"));
        
        verify(courseMediaRepository, times(2)).delete(any());
    }

    @Test
    void testIsVideoFile() {
        // 使用反射测试私有方法
        assertTrue(invokeIsVideoFile("test.mp4"));
        assertTrue(invokeIsVideoFile("test.avi"));
        assertTrue(invokeIsVideoFile("test.mov"));
        assertTrue(invokeIsVideoFile("test.MP4")); // 大写扩展名
        assertFalse(invokeIsVideoFile("test.txt"));
        assertFalse(invokeIsVideoFile("test.jpg"));
        assertFalse(invokeIsVideoFile("test"));
    }

    private boolean invokeIsVideoFile(String filename) {
        try {
            return (Boolean) ReflectionTestUtils.invokeMethod(mediaService, "isVideoFile", filename);
        } catch (Exception e) {
            return false;
        }
    }
}
