package com.nseedu.courseeval.service;

import com.nseedu.courseeval.dto.TaskResponse;
import com.nseedu.courseeval.dto.TaskStatusResponse;
import com.nseedu.courseeval.entity.*;
import com.nseedu.courseeval.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class TaskService {
    
    @Autowired
    private EvaluationTaskRepository evaluationTaskRepository;
    
    @Autowired
    private EvaluationScoreRepository evaluationScoreRepository;
    
    @Autowired
    private CourseRepository courseRepository;
    
    @Autowired
    private RubricVersionRepository rubricVersionRepository;
    
    // 动态获取默认值 - 优先使用新的7维度评判标准
    private Long getDefaultRubricVersionId() {
        try {
            // 优先使用新创建的7维度评判标准版本 (version ID = 9)
            Optional<RubricVersion> newVersion = rubricVersionRepository.findById(9L);
            if (newVersion.isPresent()) {
                System.out.println("✅ 使用7维度评判标准版本: ID=" + newVersion.get().getId());
                return newVersion.get().getId();
            }

            // 如果没有找到特定版本，使用任何可用的版本
            List<RubricVersion> allVersions = rubricVersionRepository.findAll();
            if (!allVersions.isEmpty()) {
                Long versionId = allVersions.get(0).getId();
                System.out.println("✅ 使用可用版本: ID=" + versionId);
                return versionId;
            }

            // 如果数据库中没有任何rubric版本，返回默认值
            System.out.println("⚠️ 未找到评估标准版本，使用默认值: ID=1");
            return 1L;

        } catch (Exception e) {
            System.err.println("❌ 获取评估标准版本失败: " + e.getMessage());
            // 发生异常时返回默认值
            return 1L;
        }
    }
    
    private Long getDefaultEvaluatorId() {
        // 简化处理：使用第一个用户，实际应用中应该从session获取
        return 1L; // 临时硬编码，后续可以优化
    }
    
    /**
     * 创建评估任务
     */
    public TaskResponse createEvaluationTask(Long courseId) {
        System.out.println("🚀 开始创建评估任务: courseId=" + courseId);

        try {
            // 验证课程是否存在
            Optional<Course> courseOpt = courseRepository.findById(courseId);
            if (courseOpt.isEmpty()) {
                throw new RuntimeException("课程不存在: " + courseId);
            }
            System.out.println("✅ 课程验证通过: " + courseOpt.get().getTitle());

            // 获取默认配置
            Long rubricVersionId = getDefaultRubricVersionId();
            Long evaluatorId = getDefaultEvaluatorId();
            System.out.println("✅ 获取默认配置: rubricVersionId=" + rubricVersionId + ", evaluatorId=" + evaluatorId);

            // 创建评估任务
            EvaluationTask task = new EvaluationTask();
            task.setCourseId(courseId);
            task.setRubricVersionId(rubricVersionId);
            task.setEvaluatorId(evaluatorId);
            task.setStatus(EvaluationStatus.PENDING);
            task.setCreatedAt(LocalDateTime.now());

            EvaluationTask savedTask = evaluationTaskRepository.save(task);
            System.out.println("✅ 评估任务保存成功: taskId=" + savedTask.getId());

            TaskResponse response = new TaskResponse(savedTask.getId(), savedTask.getStatus().toString());
            System.out.println("🎉 创建评估任务完成: " + response);

            return response;

        } catch (Exception e) {
            System.err.println("❌ 创建评估任务异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 查询任务状态
     */
    public TaskStatusResponse getTaskStatus(Long taskId) {
        Optional<EvaluationTask> taskOpt = evaluationTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("评估任务不存在: " + taskId);
        }
        
        EvaluationTask task = taskOpt.get();
        String status = task.getStatus().toString();
        
        // 如果任务已完成，获取雷达图数据
        if (EvaluationStatus.DONE.equals(task.getStatus())) {
            List<EvaluationScore> scores = evaluationScoreRepository.findByTaskIdWithDimension(taskId);
            List<TaskStatusResponse.RadarPoint> radarData = scores.stream()
                .map(score -> new TaskStatusResponse.RadarPoint(
                    score.getDimension().getName(),
                    normalizeScore(score.getScore(), score.getDimension().getMaxScore())
                ))
                .collect(Collectors.toList());
            
            return new TaskStatusResponse(taskId, status, radarData);
        }
        
        return new TaskStatusResponse(taskId, status);
    }
    
    /**
     * 将分数标准化为100分制
     */
    private Integer normalizeScore(Double score, Double maxScore) {
        if (score == null || maxScore == null || maxScore == 0) {
            return 80;
        }
        return (int) Math.round((score / maxScore) * 100);
    }
} 