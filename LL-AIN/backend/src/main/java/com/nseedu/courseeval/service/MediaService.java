package com.nseedu.courseeval.service;

import com.nseedu.courseeval.entity.CourseMedia;
import com.nseedu.courseeval.entity.MediaType;
import com.nseedu.courseeval.entity.StorageType;
import com.nseedu.courseeval.repository.CourseMediaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MediaService {

    @Value("${file.upload-dir}")
    private String uploadDir;

    @Autowired
    private CourseMediaRepository courseMediaRepository;

    /**
     * 上传媒体文件
     */
    public Map<String, Object> uploadMedia(MultipartFile file, Long courseId) throws Exception {
        // 1. 文件类型双重校验
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // Content-Type 校验
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("video/")) {
            throw new IllegalArgumentException("只支持视频文件");
        }

        // 文件扩展名校验
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isVideoFile(originalFilename)) {
            throw new IllegalArgumentException("不支持的视频格式");
        }

        // 2. 生成唯一文件名 (timestamp_uuid_originalName)
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        String newFileName = timestamp + "_" + uuid + "_" + originalFilename;

        // 3. 确保上传目录存在
        Path uploadPath = Paths.get(uploadDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }

        // 4. 保存文件
        Path filePath = uploadPath.resolve(newFileName);
        file.transferTo(filePath.toFile());

        // 5. 创建CourseMedia记录并保存到数据库
        CourseMedia courseMedia = new CourseMedia();
        courseMedia.setCourseId(courseId);
        courseMedia.setMediaType(MediaType.VIDEO);
        courseMedia.setStorageType(StorageType.LOCAL);
        courseMedia.setFilePath(filePath.toAbsolutePath().toString());
        courseMedia.setSizeBytes(file.getSize());
        courseMedia.setCreatedAt(LocalDateTime.now());
        
        // 保存到数据库
        CourseMedia savedMedia = courseMediaRepository.save(courseMedia);

        // 输出保存路径到控制台
        System.out.println("📁 文件保存路径: " + filePath.toAbsolutePath());
        System.out.println("📹 Media ID: " + savedMedia.getId());
        System.out.println("📄 原始文件名: " + originalFilename);
        System.out.println("📚 关联课程ID: " + courseId);

        // 6. 返回结果
        Map<String, Object> response = new HashMap<>();
        response.put("mediaId", savedMedia.getId().toString());
        response.put("fileName", originalFilename);
        response.put("courseId", courseId);
        response.put("savedPath", filePath.toAbsolutePath().toString());
        response.put("success", true);

        return response;
    }

    /**
     * 获取课程的所有媒体文件
     */
    public List<Map<String, Object>> getCourseMedia(Long courseId) {
        List<CourseMedia> mediaList = courseMediaRepository.findByCourseIdOrderByCreatedAt(courseId);
        
        return mediaList.stream().map(media -> {
            Map<String, Object> mediaInfo = new HashMap<>();
            mediaInfo.put("id", media.getId());
            mediaInfo.put("mediaType", media.getMediaType().toString());
            mediaInfo.put("storageType", media.getStorageType().toString());
            mediaInfo.put("sizeBytes", media.getSizeBytes());
            mediaInfo.put("durationSec", media.getDurationSec());
            mediaInfo.put("createdAt", media.getCreatedAt());
            
            // 提取原始文件名（去掉时间戳和UUID前缀）
            String filePath = media.getFilePath();
            if (filePath != null) {
                String fileName = Paths.get(filePath).getFileName().toString();
                // 格式: yyyyMMdd_HHmmss_uuid_originalName
                String[] parts = fileName.split("_", 4);
                if (parts.length >= 4) {
                    mediaInfo.put("originalFileName", parts[3]);
                } else {
                    mediaInfo.put("originalFileName", fileName);
                }
            }
            
            return mediaInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取单个媒体文件信息
     */
    public Optional<Map<String, Object>> getMediaInfo(Long mediaId) {
        Optional<CourseMedia> mediaOpt = courseMediaRepository.findById(mediaId);
        if (mediaOpt.isEmpty()) {
            return Optional.empty();
        }
        
        CourseMedia media = mediaOpt.get();
        Map<String, Object> response = new HashMap<>();
        response.put("id", media.getId());
        response.put("courseId", media.getCourseId());
        response.put("mediaType", media.getMediaType().toString());
        response.put("storageType", media.getStorageType().toString());
        response.put("sizeBytes", media.getSizeBytes());
        response.put("durationSec", media.getDurationSec());
        response.put("createdAt", media.getCreatedAt());
        
        // 提取原始文件名
        String filePath = media.getFilePath();
        if (filePath != null) {
            String fileName = Paths.get(filePath).getFileName().toString();
            String[] parts = fileName.split("_", 4);
            if (parts.length >= 4) {
                response.put("originalFileName", parts[3]);
            } else {
                response.put("originalFileName", fileName);
            }
        }
        
        return Optional.of(response);
    }

    /**
     * 删除媒体文件
     */
    public boolean deleteMedia(Long mediaId) throws Exception {
        Optional<CourseMedia> mediaOpt = courseMediaRepository.findById(mediaId);
        if (mediaOpt.isEmpty()) {
            return false;
        }
        
        CourseMedia media = mediaOpt.get();
        
        // 删除物理文件
        if (media.getFilePath() != null && !media.getFilePath().isEmpty()) {
            Path filePath = Paths.get(media.getFilePath());
            try {
                Files.deleteIfExists(filePath);
                System.out.println("🗑️ 物理文件已删除: " + filePath);
            } catch (IOException e) {
                System.err.println("⚠️ 删除物理文件失败: " + e.getMessage());
                // 继续删除数据库记录，即使物理文件删除失败
            }
        }
        
        // 删除数据库记录
        courseMediaRepository.delete(media);
        System.out.println("🗑️ 媒体记录已删除: ID=" + mediaId);
        
        return true;
    }

    /**
     * 获取媒体文件资源用于下载/访问
     */
    public MediaResource getMediaResource(Long mediaId) throws Exception {
        Optional<CourseMedia> mediaOpt = courseMediaRepository.findById(mediaId);
        if (mediaOpt.isEmpty()) {
            return null;
        }
        
        CourseMedia media = mediaOpt.get();
        String filePath = media.getFilePath();
        
        if (filePath == null || filePath.isEmpty()) {
            throw new IllegalStateException("媒体文件路径为空");
        }
        
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            throw new IllegalStateException("文件不存在: " + filePath);
        }
        
        Resource resource = new UrlResource(path.toUri());
        if (!resource.exists() || !resource.isReadable()) {
            throw new IllegalStateException("文件不可读: " + filePath);
        }
        
        // 获取原始文件名
        String fileName = path.getFileName().toString();
        String[] parts = fileName.split("_", 4);
        String originalFileName = parts.length >= 4 ? parts[3] : fileName;
        
        // 设置Content-Type
        String contentType = getContentType(originalFileName);
        
        return new MediaResource(resource, originalFileName, contentType);
    }

    /**
     * 检查是否为视频文件
     */
    private boolean isVideoFile(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        return extension.matches("\\.(mp4|avi|mov|wmv|flv|webm|mkv)");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDot = filename.lastIndexOf('.');
        return lastDot > 0 ? filename.substring(lastDot) : "";
    }

    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        switch (extension) {
            case ".mp4":
                return "video/mp4";
            case ".avi":
                return "video/x-msvideo";
            case ".mov":
                return "video/quicktime";
            case ".wmv":
                return "video/x-ms-wmv";
            case ".flv":
                return "video/x-flv";
            case ".webm":
                return "video/webm";
            case ".mkv":
                return "video/x-matroska";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 批量删除媒体文件
     */
    public Map<String, Object> batchDeleteMedia(List<Long> mediaIds) {
        Map<String, Object> result = new HashMap<>();
        List<Long> successIds = new ArrayList<>();
        List<Map<String, Object>> failedIds = new ArrayList<>();

        for (Long mediaId : mediaIds) {
            try {
                boolean deleted = deleteMedia(mediaId);
                if (deleted) {
                    successIds.add(mediaId);
                } else {
                    Map<String, Object> failedItem = new HashMap<>();
                    failedItem.put("mediaId", mediaId);
                    failedItem.put("reason", "媒体文件不存在");
                    failedIds.add(failedItem);
                }
            } catch (Exception e) {
                Map<String, Object> failedItem = new HashMap<>();
                failedItem.put("mediaId", mediaId);
                failedItem.put("reason", e.getMessage());
                failedIds.add(failedItem);
            }
        }

        result.put("successCount", successIds.size());
        result.put("failedCount", failedIds.size());
        result.put("successIds", successIds);
        result.put("failedItems", failedIds);

        return result;
    }

    /**
     * 获取课程媒体统计信息
     */
    public Map<String, Object> getCourseMediaStats(Long courseId) {
        List<CourseMedia> mediaList = courseMediaRepository.findByCourseIdOrderByCreatedAt(courseId);

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCount", mediaList.size());

        // 按媒体类型统计
        Map<String, Long> typeStats = mediaList.stream()
                .collect(Collectors.groupingBy(
                    media -> media.getMediaType().toString(),
                    Collectors.counting()
                ));
        stats.put("typeStats", typeStats);

        // 按存储类型统计
        Map<String, Long> storageStats = mediaList.stream()
                .collect(Collectors.groupingBy(
                    media -> media.getStorageType().toString(),
                    Collectors.counting()
                ));
        stats.put("storageStats", storageStats);

        // 总文件大小
        long totalSize = mediaList.stream()
                .filter(media -> media.getSizeBytes() != null)
                .mapToLong(CourseMedia::getSizeBytes)
                .sum();
        stats.put("totalSizeBytes", totalSize);
        stats.put("totalSizeMB", totalSize / (1024.0 * 1024.0));

        // 最新上传时间
        Optional<LocalDateTime> latestUpload = mediaList.stream()
                .map(CourseMedia::getCreatedAt)
                .filter(Objects::nonNull)
                .max(LocalDateTime::compareTo);
        stats.put("latestUpload", latestUpload.orElse(null));

        return stats;
    }

    /**
     * 根据媒体类型获取课程媒体文件
     */
    public List<Map<String, Object>> getCourseMediaByType(Long courseId, MediaType mediaType) {
        List<CourseMedia> mediaList = courseMediaRepository.findByCourseIdAndMediaType(courseId, mediaType);

        return mediaList.stream().map(media -> {
            Map<String, Object> mediaInfo = new HashMap<>();
            mediaInfo.put("id", media.getId());
            mediaInfo.put("mediaType", media.getMediaType().toString());
            mediaInfo.put("storageType", media.getStorageType().toString());
            mediaInfo.put("sizeBytes", media.getSizeBytes());
            mediaInfo.put("durationSec", media.getDurationSec());
            mediaInfo.put("createdAt", media.getCreatedAt());

            // 提取原始文件名
            String filePath = media.getFilePath();
            if (filePath != null) {
                String fileName = Paths.get(filePath).getFileName().toString();
                String[] parts = fileName.split("_", 4);
                if (parts.length >= 4) {
                    mediaInfo.put("originalFileName", parts[3]);
                } else {
                    mediaInfo.put("originalFileName", fileName);
                }
            }

            return mediaInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 媒体资源包装类
     */
    public static class MediaResource {
        private final Resource resource;
        private final String originalFileName;
        private final String contentType;

        public MediaResource(Resource resource, String originalFileName, String contentType) {
            this.resource = resource;
            this.originalFileName = originalFileName;
            this.contentType = contentType;
        }

        public Resource getResource() { return resource; }
        public String getOriginalFileName() { return originalFileName; }
        public String getContentType() { return contentType; }
    }
}
