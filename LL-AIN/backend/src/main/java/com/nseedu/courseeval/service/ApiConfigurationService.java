package com.nseedu.courseeval.service;

import com.nseedu.courseeval.config.AliyunConfig;
import com.nseedu.courseeval.config.QwenConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * API配置检查服务
 * 检查阿里云和Qwen API的配置状态
 */
@Service
public class ApiConfigurationService {

    @Autowired
    private AliyunConfig aliyunConfig;

    @Autowired
    private QwenConfig qwenConfig;

    @Value("${ai.enabled:true}")
    private boolean aiEnabled;

    /**
     * 检查API配置状态
     */
    public ApiConfigStatus checkApiConfiguration() {
        ApiConfigStatus status = new ApiConfigStatus();
        
        status.aiEnabled = aiEnabled;
        status.aliyunConfigured = isAliyunConfigured();
        status.qwenConfigured = isQwenConfigured();
        status.canUseRealAI = status.aiEnabled && status.aliyunConfigured && status.qwenConfigured;
        
        return status;
    }

    /**
     * 检查阿里云配置
     */
    private boolean isAliyunConfigured() {
        try {
            // 直接从系统属性读取，确保获取最新值
            String accessKeyId = System.getProperty("ALIYUN_ACCESS_KEY_ID");
            String accessKeySecret = System.getProperty("ALIYUN_ACCESS_KEY_SECRET");
            String nlsAppKey = System.getProperty("ALIYUN_NLS_APP_KEY");
            String nlsDomain = System.getProperty("ALIYUN_NLS_DOMAIN");

            boolean configured = accessKeyId != null && !accessKeyId.isEmpty() && !accessKeyId.startsWith("${") && !accessKeyId.contains("placeholder")
                && accessKeySecret != null && !accessKeySecret.isEmpty() && !accessKeySecret.startsWith("${") && !accessKeySecret.contains("placeholder")
                && nlsAppKey != null && !nlsAppKey.isEmpty() && !nlsAppKey.startsWith("${") && !nlsAppKey.contains("placeholder")
                && nlsDomain != null && !nlsDomain.isEmpty() && !nlsDomain.startsWith("${");

            System.out.println("🔍 阿里云配置检查:");
            System.out.println("   AccessKeyId: " + (accessKeyId != null && !accessKeyId.isEmpty() && !accessKeyId.startsWith("${") ? "✅" : "❌"));
            System.out.println("   AccessKeySecret: " + (accessKeySecret != null && !accessKeySecret.isEmpty() && !accessKeySecret.startsWith("${") ? "✅" : "❌"));
            System.out.println("   NLS AppKey: " + (nlsAppKey != null && !nlsAppKey.isEmpty() && !nlsAppKey.startsWith("${") ? "✅" : "❌"));
            System.out.println("   NLS Domain: " + (nlsDomain != null && !nlsDomain.isEmpty() && !nlsDomain.startsWith("${") ? "✅" : "❌"));

            return configured;
        } catch (Exception e) {
            System.err.println("❌ 阿里云配置检查异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查Qwen配置
     */
    private boolean isQwenConfigured() {
        try {
            // 直接从系统属性读取，确保获取最新值
            String apiKey = System.getProperty("QWEN_API_KEY");
            String apiUrl = System.getProperty("QWEN_API_URL");
            String modelName = System.getProperty("QWEN_MODEL_NAME");

            boolean configured = apiKey != null && !apiKey.isEmpty() && !apiKey.startsWith("${") && !apiKey.contains("placeholder")
                && apiUrl != null && !apiUrl.isEmpty() && !apiUrl.startsWith("${")
                && modelName != null && !modelName.isEmpty() && !modelName.startsWith("${");

            System.out.println("🔍 Qwen配置检查:");
            System.out.println("   API Key: " + (apiKey != null && !apiKey.isEmpty() && !apiKey.startsWith("${") ? "✅" : "❌"));
            System.out.println("   API URL: " + (apiUrl != null && !apiUrl.isEmpty() && !apiUrl.startsWith("${") ? "✅" : "❌"));
            System.out.println("   Model Name: " + (modelName != null && !modelName.isEmpty() && !modelName.startsWith("${") ? "✅" : "❌"));

            return configured;
        } catch (Exception e) {
            System.err.println("❌ Qwen配置检查异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 打印配置状态
     */
    public void printConfigurationStatus() {
        ApiConfigStatus status = checkApiConfiguration();
        
        System.out.println("=== API配置状态检查 ===");
        System.out.println("🤖 AI功能启用: " + (status.aiEnabled ? "✅ 是" : "❌ 否"));
        System.out.println("☁️ 阿里云配置: " + (status.aliyunConfigured ? "✅ 已配置" : "❌ 未配置"));
        System.out.println("🧠 Qwen配置: " + (status.qwenConfigured ? "✅ 已配置" : "❌ 未配置"));
        System.out.println("🚀 可使用真实AI: " + (status.canUseRealAI ? "✅ 是" : "❌ 否"));
        
        if (!status.canUseRealAI) {
            System.out.println("⚠️ 将使用智能模拟模式");
            if (!status.aliyunConfigured) {
                System.out.println("   - 请在.env文件中配置阿里云API密钥");
            }
            if (!status.qwenConfigured) {
                System.out.println("   - 请在.env文件中配置Qwen API密钥");
            }
        }
        System.out.println("========================");
    }

    /**
     * API配置状态
     */
    public static class ApiConfigStatus {
        public boolean aiEnabled;
        public boolean aliyunConfigured;
        public boolean qwenConfigured;
        public boolean canUseRealAI;
    }
}
