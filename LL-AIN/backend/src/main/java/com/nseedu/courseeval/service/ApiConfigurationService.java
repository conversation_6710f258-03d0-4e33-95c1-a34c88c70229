package com.nseedu.courseeval.service;

import com.nseedu.courseeval.config.AliyunConfig;
import com.nseedu.courseeval.config.QwenConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * API配置检查服务
 * 检查阿里云和Qwen API的配置状态
 */
@Service
public class ApiConfigurationService {

    @Autowired
    private AliyunConfig aliyunConfig;

    @Autowired
    private QwenConfig qwenConfig;

    @Value("${ai.enabled:true}")
    private boolean aiEnabled;

    /**
     * 检查API配置状态
     */
    public ApiConfigStatus checkApiConfiguration() {
        ApiConfigStatus status = new ApiConfigStatus();
        
        status.aiEnabled = aiEnabled;
        status.aliyunConfigured = isAliyunConfigured();
        status.qwenConfigured = isQwenConfigured();
        status.canUseRealAI = status.aiEnabled && status.aliyunConfigured && status.qwenConfigured;
        
        return status;
    }

    /**
     * 检查阿里云配置
     */
    private boolean isAliyunConfigured() {
        try {
            return aliyunConfig.getAccessKeyId() != null 
                && !aliyunConfig.getAccessKeyId().isEmpty()
                && !aliyunConfig.getAccessKeyId().contains("placeholder")
                && aliyunConfig.getAccessKeySecret() != null
                && !aliyunConfig.getAccessKeySecret().isEmpty()
                && !aliyunConfig.getAccessKeySecret().contains("placeholder")
                && aliyunConfig.getNls().getAppKey() != null
                && !aliyunConfig.getNls().getAppKey().isEmpty()
                && !aliyunConfig.getNls().getAppKey().contains("placeholder");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查Qwen配置
     */
    private boolean isQwenConfigured() {
        try {
            return qwenConfig.getApiKey() != null 
                && !qwenConfig.getApiKey().isEmpty()
                && !qwenConfig.getApiKey().contains("placeholder")
                && qwenConfig.getApiUrl() != null
                && !qwenConfig.getApiUrl().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 打印配置状态
     */
    public void printConfigurationStatus() {
        ApiConfigStatus status = checkApiConfiguration();
        
        System.out.println("=== API配置状态检查 ===");
        System.out.println("🤖 AI功能启用: " + (status.aiEnabled ? "✅ 是" : "❌ 否"));
        System.out.println("☁️ 阿里云配置: " + (status.aliyunConfigured ? "✅ 已配置" : "❌ 未配置"));
        System.out.println("🧠 Qwen配置: " + (status.qwenConfigured ? "✅ 已配置" : "❌ 未配置"));
        System.out.println("🚀 可使用真实AI: " + (status.canUseRealAI ? "✅ 是" : "❌ 否"));
        
        if (!status.canUseRealAI) {
            System.out.println("⚠️ 将使用智能模拟模式");
            if (!status.aliyunConfigured) {
                System.out.println("   - 请在.env文件中配置阿里云API密钥");
            }
            if (!status.qwenConfigured) {
                System.out.println("   - 请在.env文件中配置Qwen API密钥");
            }
        }
        System.out.println("========================");
    }

    /**
     * API配置状态
     */
    public static class ApiConfigStatus {
        public boolean aiEnabled;
        public boolean aliyunConfigured;
        public boolean qwenConfigured;
        public boolean canUseRealAI;
    }
}
