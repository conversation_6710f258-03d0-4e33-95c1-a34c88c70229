package com.nseedu.courseeval.controller;

import com.nseedu.courseeval.entity.MediaType;
import com.nseedu.courseeval.service.MediaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/media")
@CrossOrigin(origins = {"http://localhost:5173","http://localhost:5174" })
public class MediaController {

    @Autowired
    private MediaService mediaService;

    @PostMapping("/upload")
    public ResponseEntity<?> uploadMedia(
            @RequestParam("file") MultipartFile file,
            @RequestParam("courseId") Long courseId) {

        System.out.println("🔍 收到上传请求 - 文件名: " + file.getOriginalFilename() + ", 课程ID: " + courseId);
        System.out.println("📄 文件大小: " + file.getSize() + " bytes");
        System.out.println("📋 Content-Type: " + file.getContentType());

        try {
            Map<String, Object> response = mediaService.uploadMedia(file, courseId);
            System.out.println("✅ 上传成功: " + response);
            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            System.err.println("❌ 参数错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            System.err.println("❌ 文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 获取课程的所有媒体文件
     */
    @GetMapping("/course/{courseId}")
    public ResponseEntity<?> getCourseMedia(@PathVariable Long courseId) {
        try {
            List<Map<String, Object>> response = mediaService.getCourseMedia(courseId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ 获取课程媒体文件失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取媒体文件失败: " + e.getMessage()));
        }
    }

    /**
     * 获取单个媒体文件信息
     */
    @GetMapping("/{mediaId}")
    public ResponseEntity<?> getMediaInfo(@PathVariable Long mediaId) {
        try {
            Optional<Map<String, Object>> response = mediaService.getMediaInfo(mediaId);
            if (response.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(response.get());

        } catch (Exception e) {
            System.err.println("❌ 获取媒体文件信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取媒体文件信息失败: " + e.getMessage()));
        }
    }

    /**
     * 删除媒体文件
     */
    @DeleteMapping("/{mediaId}")
    public ResponseEntity<?> deleteMedia(@PathVariable Long mediaId) {
        try {
            boolean deleted = mediaService.deleteMedia(mediaId);
            if (!deleted) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(Map.of("success", true, "message", "媒体文件删除成功"));

        } catch (Exception e) {
            System.err.println("❌ 删除媒体文件失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "删除媒体文件失败: " + e.getMessage()));
        }
    }

    /**
     * 下载/访问媒体文件
     */
    @GetMapping("/{mediaId}/download")
    public ResponseEntity<Resource> downloadMedia(@PathVariable Long mediaId) {
        try {
            MediaService.MediaResource mediaResource = mediaService.getMediaResource(mediaId);
            if (mediaResource == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok()
                    .contentType(org.springframework.http.MediaType.parseMediaType(mediaResource.getContentType()))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + mediaResource.getOriginalFileName() + "\"")
                    .body(mediaResource.getResource());

        } catch (IllegalStateException e) {
            System.err.println("❌ 文件状态错误: " + e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            System.err.println("❌ 下载媒体文件失败: " + e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 流式播放媒体文件（用于视频播放器）
     */
    @GetMapping("/{mediaId}/stream")
    public ResponseEntity<Resource> streamMedia(@PathVariable Long mediaId) {
        try {
            MediaService.MediaResource mediaResource = mediaService.getMediaResource(mediaId);
            if (mediaResource == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok()
                    .contentType(org.springframework.http.MediaType.parseMediaType(mediaResource.getContentType()))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline")
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .header(HttpHeaders.EXPIRES, "0")
                    .body(mediaResource.getResource());

        } catch (IllegalStateException e) {
            System.err.println("❌ 文件状态错误: " + e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            System.err.println("❌ 流式播放媒体文件失败: " + e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取媒体文件的播放URL
     */
    @GetMapping("/{mediaId}/url")
    public ResponseEntity<?> getMediaUrl(@PathVariable Long mediaId) {
        try {
            Optional<Map<String, Object>> mediaInfo = mediaService.getMediaInfo(mediaId);
            if (mediaInfo.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            // 构建流式播放URL
            String streamUrl = "/api/v1/media/" + mediaId + "/stream";

            Map<String, Object> response = new HashMap<>();
            response.put("mediaId", mediaId);
            response.put("streamUrl", streamUrl);
            response.put("downloadUrl", "/api/v1/media/" + mediaId + "/download");
            response.put("mediaInfo", mediaInfo.get());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ 获取媒体URL失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取媒体URL失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除媒体文件
     */
    @DeleteMapping("/batch")
    public ResponseEntity<?> batchDeleteMedia(@RequestBody Map<String, List<Long>> request) {
        try {
            List<Long> mediaIds = request.get("mediaIds");
            if (mediaIds == null || mediaIds.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "媒体ID列表不能为空"));
            }

            Map<String, Object> result = mediaService.batchDeleteMedia(mediaIds);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            System.err.println("❌ 批量删除媒体文件失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "批量删除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取课程媒体统计信息
     */
    @GetMapping("/course/{courseId}/stats")
    public ResponseEntity<?> getCourseMediaStats(@PathVariable Long courseId) {
        try {
            Map<String, Object> stats = mediaService.getCourseMediaStats(courseId);
            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            System.err.println("❌ 获取课程媒体统计失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 根据媒体类型获取课程媒体文件
     */
    @GetMapping("/course/{courseId}/type/{mediaType}")
    public ResponseEntity<?> getCourseMediaByType(
            @PathVariable Long courseId,
            @PathVariable String mediaType) {
        try {
            MediaType type = MediaType.valueOf(mediaType.toUpperCase());
            List<Map<String, Object>> response = mediaService.getCourseMediaByType(courseId, type);
            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", "不支持的媒体类型: " + mediaType));
        } catch (Exception e) {
            System.err.println("❌ 获取指定类型媒体文件失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取媒体文件失败: " + e.getMessage()));
        }
    }

    /**
     * 测试上传端点（绕过文件类型验证）
     */
    @PostMapping("/test-upload")
    public ResponseEntity<?> testUpload(
            @RequestParam("file") MultipartFile file,
            @RequestParam("courseId") Long courseId) {

        System.out.println("🧪 测试上传 - 文件名: " + file.getOriginalFilename() + ", 课程ID: " + courseId);
        System.out.println("📄 文件大小: " + file.getSize() + " bytes");
        System.out.println("📋 Content-Type: " + file.getContentType());

        try {
            // 简单的响应，不实际保存文件
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "测试上传成功");
            response.put("fileName", file.getOriginalFilename());
            response.put("fileSize", file.getSize());
            response.put("contentType", file.getContentType());
            response.put("courseId", courseId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ 测试上传失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "测试上传失败: " + e.getMessage()));
        }
    }
}
