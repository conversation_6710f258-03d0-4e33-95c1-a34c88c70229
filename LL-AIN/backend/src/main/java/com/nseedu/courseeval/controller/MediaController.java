package com.nseedu.courseeval.controller;

import com.nseedu.courseeval.entity.CourseMedia;
import com.nseedu.courseeval.entity.MediaType;
import com.nseedu.courseeval.entity.StorageType;
import com.nseedu.courseeval.repository.CourseMediaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/media")
@CrossOrigin(origins = "http://localhost:5173")
public class MediaController {

    @Value("${file.upload-dir}")
    private String uploadDir;

    @Autowired
    private CourseMediaRepository courseMediaRepository;

    @PostMapping("/upload")
    public ResponseEntity<?> uploadMedia(
            @RequestParam("file") MultipartFile file,
            @RequestParam("courseId") Long courseId) {
        
        System.out.println("🔍 收到上传请求 - 文件名: " + file.getOriginalFilename() + ", 课程ID: " + courseId);
        
        try {
            // 添加调试日志
            System.out.println("📁 配置的上传目录: " + uploadDir);
            
            // 1. 文件类型双重校验
            if (file.isEmpty()) {
                System.out.println("❌ 文件为空");
                return ResponseEntity.badRequest().body(Map.of("error", "文件不能为空"));
            }

            // Content-Type 校验
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("video/")) {
                return ResponseEntity.badRequest().body(Map.of("error", "只支持视频文件"));
            }

            // 文件扩展名校验
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isVideoFile(originalFilename)) {
                return ResponseEntity.badRequest().body(Map.of("error", "不支持的视频格式"));
            }

            // 2. 生成唯一文件名 (timestamp_uuid_originalName)
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            String newFileName = timestamp + "_" + uuid + "_" + originalFilename;

            // 3. 确保上传目录存在
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // 4. 保存文件
            Path filePath = uploadPath.resolve(newFileName);
            file.transferTo(filePath.toFile());

            // 5. 创建CourseMedia记录并保存到数据库
            CourseMedia courseMedia = new CourseMedia();
            courseMedia.setCourseId(courseId);
            courseMedia.setMediaType(MediaType.VIDEO);
            courseMedia.setStorageType(StorageType.LOCAL);
            courseMedia.setFilePath(filePath.toAbsolutePath().toString());
            courseMedia.setSizeBytes(file.getSize());
            courseMedia.setCreatedAt(LocalDateTime.now());
            
            // 保存到数据库
            CourseMedia savedMedia = courseMediaRepository.save(courseMedia);

            // 输出保存路径到控制台
            System.out.println("📁 文件保存路径: " + filePath.toAbsolutePath());
            System.out.println("📹 Media ID: " + savedMedia.getId());
            System.out.println("📄 原始文件名: " + originalFilename);
            System.out.println("📚 关联课程ID: " + courseId);

            // 6. 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("mediaId", savedMedia.getId().toString()); // 使用数据库ID作为mediaId
            response.put("fileName", originalFilename);
            response.put("courseId", courseId);
            response.put("savedPath", filePath.toAbsolutePath().toString());
            response.put("success", true);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ 文件上传失败: " + e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "文件上传失败: " + e.getMessage()));
        }
    }

    private boolean isVideoFile(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        return extension.matches("\\.(mp4|avi|mov|wmv|flv|webm|mkv)");
    }

    private String getFileExtension(String filename) {
        int lastDot = filename.lastIndexOf('.');
        return lastDot > 0 ? filename.substring(lastDot) : "";
    }
} 
