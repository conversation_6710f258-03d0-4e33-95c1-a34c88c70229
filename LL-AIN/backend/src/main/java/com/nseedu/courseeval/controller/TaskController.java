package com.nseedu.courseeval.controller;

import com.nseedu.courseeval.dto.CreateTaskRequest;
import com.nseedu.courseeval.dto.TaskResponse;
import com.nseedu.courseeval.dto.TaskStatusResponse;
import com.nseedu.courseeval.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/tasks")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:5174", "http://127.0.0.1:5173", "http://127.0.0.1:5174"})
public class TaskController {
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 创建评估任务
     * POST /api/v1/tasks
     */
    @PostMapping
    public ResponseEntity<?> createTask(@RequestBody CreateTaskRequest request) {
        try {
            System.out.println("📥 收到创建评估任务请求: courseId=" + request.getCourseId());

            TaskResponse response = taskService.createEvaluationTask(request.getCourseId());

            System.out.println("✅ 评估任务创建成功: taskId=" + response.getTaskId());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ 创建评估任务失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                    .body(Map.of("error", "创建评估任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 查询任务状态
     * GET /api/v1/tasks/{taskId}
     */
    @GetMapping("/{taskId}")
    public ResponseEntity<TaskStatusResponse> getTaskStatus(@PathVariable Long taskId) {
        try {
            TaskStatusResponse response = taskService.getTaskStatus(taskId);
            
            System.out.println("📊 查询任务状态: taskId=" + taskId + ", status=" + response.getStatus());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("❌ 查询任务状态失败: " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }
} 