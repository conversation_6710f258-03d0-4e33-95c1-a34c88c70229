package com.nseedu.courseeval.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置刷新服务
 * 确保环境变量正确加载到Spring配置中
 */
@Service
public class ConfigurationRefreshService {

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void refreshConfiguration() {
        try {
            // 等待环境变量加载完成
            Thread.sleep(1000);
            
            System.out.println("🔄 开始刷新Spring配置...");
            
            // 获取当前环境
            ConfigurableEnvironment environment = (ConfigurableEnvironment) applicationContext.getEnvironment();
            
            // 创建新的属性源，包含系统属性中的环境变量
            Map<String, Object> envProperties = new HashMap<>();
            
            // 从系统属性中提取环境变量
            System.getProperties().forEach((key, value) -> {
                String keyStr = key.toString();
                if (keyStr.startsWith("ALIYUN_") || keyStr.startsWith("QWEN_") || keyStr.equals("AI_ENABLED")) {
                    envProperties.put(keyStr, value);
                    System.out.println("🔧 添加到Spring环境: " + keyStr + " = " + 
                        (keyStr.contains("KEY") || keyStr.contains("SECRET") ? "***" : value));
                }
            });
            
            // 添加属性源到环境中（高优先级）
            if (!envProperties.isEmpty()) {
                MapPropertySource propertySource = new MapPropertySource("dotenvProperties", envProperties);
                environment.getPropertySources().addFirst(propertySource);
                System.out.println("✅ Spring配置刷新完成，添加了 " + envProperties.size() + " 个环境变量");
            }
            
            // 验证配置是否生效
            validateSpringConfiguration(environment);
            
        } catch (Exception e) {
            System.err.println("❌ 配置刷新失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证Spring配置
     */
    private void validateSpringConfiguration(ConfigurableEnvironment environment) {
        System.out.println("🔍 验证Spring配置:");
        
        String[] configs = {
            "aliyun.access-key-id",
            "aliyun.access-key-secret",
            "aliyun.nls.app-key", 
            "aliyun.nls.domain",
            "qwen.api-key",
            "qwen.api-url"
        };
        
        for (String config : configs) {
            String value = environment.getProperty(config);
            if (value != null && !value.isEmpty() && !value.startsWith("${")) {
                System.out.println("   ✅ " + config + ": " + 
                    (config.contains("key") || config.contains("secret") ? "***" : value));
            } else {
                System.out.println("   ❌ " + config + ": " + value);
            }
        }
    }
}
