package com.nseedu.courseeval.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "aliyun")
public class AliyunConfig {

    @Value("${ALIYUN_ACCESS_KEY_ID:}")
    private String accessKeyId;

    @Value("${ALIYUN_ACCESS_KEY_SECRET:}")
    private String accessKeySecret;

    private NlsConfig nls;
    private OssConfig oss;
    
    // 构造函数
    public AliyunConfig() {
        this.nls = new NlsConfig();
        this.oss = new OssConfig();
        // 环境变量将在第一次访问时延迟加载
    }

    /**
     * 实时从环境变量加载配置，确保获取最新值
     */
    private void refreshFromEnvironment() {
        String accessKeyId = System.getenv("ALIYUN_ACCESS_KEY_ID");
        if (accessKeyId != null && !accessKeyId.isEmpty()) {
            this.accessKeyId = accessKeyId;
        }

        String accessKeySecret = System.getenv("ALIYUN_ACCESS_KEY_SECRET");
        if (accessKeySecret != null && !accessKeySecret.isEmpty()) {
            this.accessKeySecret = accessKeySecret;
        }

        String nlsAppKey = System.getenv("ALIYUN_NLS_APP_KEY");
        if (nlsAppKey != null && !nlsAppKey.isEmpty()) {
            this.nls.appKey = nlsAppKey;
        }

        String nlsDomain = System.getenv("ALIYUN_NLS_DOMAIN");
        if (nlsDomain != null && !nlsDomain.isEmpty()) {
            this.nls.domain = nlsDomain;
        }

        String nlsRegionId = System.getenv("ALIYUN_NLS_REGION_ID");
        if (nlsRegionId != null && !nlsRegionId.isEmpty()) {
            this.nls.regionId = nlsRegionId;
        }
    }
    
    // Getters and Setters
    public String getAccessKeyId() {
        refreshFromEnvironment();
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        refreshFromEnvironment();
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public NlsConfig getNls() {
        refreshFromEnvironment();
        return nls;
    }
    
    public void setNls(NlsConfig nls) {
        this.nls = nls;
    }
    
    public OssConfig getOss() {
        return oss;
    }
    
    public void setOss(OssConfig oss) {
        this.oss = oss;
    }
    
    // NLS配置内部类
    public static class NlsConfig {
        @Value("${ALIYUN_NLS_APP_KEY:}")
        private String appKey;

        @Value("${ALIYUN_NLS_REGION_ID:cn-shanghai}")
        private String regionId;

        @Value("${ALIYUN_NLS_DOMAIN:filetrans.cn-shanghai.aliyuncs.com}")
        private String domain;
        
        public String getAppKey() {
            // 直接从环境变量读取最新值
            String envAppKey = System.getenv("ALIYUN_NLS_APP_KEY");
            if (envAppKey != null && !envAppKey.isEmpty()) {
                this.appKey = envAppKey;
            }
            return appKey;
        }
        
        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }
        
        public String getRegionId() {
            return regionId;
        }
        
        public void setRegionId(String regionId) {
            this.regionId = regionId;
        }
        
        public String getDomain() {
            return domain;
        }
        
        public void setDomain(String domain) {
            this.domain = domain;
        }
    }
    
    // OSS配置内部类
    public static class OssConfig {
        private String endpoint;
        private String bucketName;
        
        public String getEndpoint() {
            return endpoint;
        }
        
        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }
        
        public String getBucketName() {
            return bucketName;
        }
        
        public void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }
    }
} 