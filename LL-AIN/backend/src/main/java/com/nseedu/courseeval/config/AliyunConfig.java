package com.nseedu.courseeval.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "aliyun")
public class AliyunConfig {

    @Value("${ALIYUN_ACCESS_KEY_ID:}")
    private String accessKeyId;

    @Value("${ALIYUN_ACCESS_KEY_SECRET:}")
    private String accessKeySecret;

    private NlsConfig nls;
    private OssConfig oss;
    
    // 构造函数
    public AliyunConfig() {
        this.nls = new NlsConfig();
        this.oss = new OssConfig();
        // 环境变量将在第一次访问时延迟加载
    }

    /**
     * 实时从环境变量加载配置，确保获取最新值
     */
    private void refreshFromEnvironment() {
        String accessKeyId = System.getenv("ALIYUN_ACCESS_KEY_ID");
        if (accessKeyId != null && !accessKeyId.isEmpty()) {
            this.accessKeyId = accessKeyId;
        }

        String accessKeySecret = System.getenv("ALIYUN_ACCESS_KEY_SECRET");
        if (accessKeySecret != null && !accessKeySecret.isEmpty()) {
            this.accessKeySecret = accessKeySecret;
        }

        String nlsAppKey = System.getenv("ALIYUN_NLS_APP_KEY");
        if (nlsAppKey != null && !nlsAppKey.isEmpty()) {
            this.nls.appKey = nlsAppKey;
        }

        String nlsDomain = System.getenv("ALIYUN_NLS_DOMAIN");
        if (nlsDomain != null && !nlsDomain.isEmpty()) {
            this.nls.domain = nlsDomain;
        }

        String nlsRegionId = System.getenv("ALIYUN_NLS_REGION_ID");
        if (nlsRegionId != null && !nlsRegionId.isEmpty()) {
            this.nls.regionId = nlsRegionId;
        }
    }
    
    // Getters and Setters
    public String getAccessKeyId() {
        refreshFromEnvironment();
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        refreshFromEnvironment();
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public NlsConfig getNls() {
        refreshFromEnvironment();
        return nls;
    }
    
    public void setNls(NlsConfig nls) {
        this.nls = nls;
    }
    
    public OssConfig getOss() {
        return oss;
    }
    
    public void setOss(OssConfig oss) {
        this.oss = oss;
    }
    
    // NLS配置内部类
    public static class NlsConfig {
        @Value("${ALIYUN_NLS_APP_KEY:}")
        private String appKey;

        @Value("${ALIYUN_NLS_REGION_ID:cn-shanghai}")
        private String regionId;

        @Value("${ALIYUN_NLS_DOMAIN:filetrans.cn-shanghai.aliyuncs.com}")
        private String domain;
        
        public String getAppKey() {
            // 尝试从多个来源读取AppKey
            String envAppKey = System.getenv("ALIYUN_NLS_APP_KEY");
            String propAppKey = System.getProperty("ALIYUN_NLS_APP_KEY");

            System.out.println("🔍 NlsConfig.getAppKey() 调用:");
            System.out.println("   环境变量 ALIYUN_NLS_APP_KEY = " + (envAppKey != null ? envAppKey.substring(0, Math.min(8, envAppKey.length())) + "***" : "null"));
            System.out.println("   系统属性 ALIYUN_NLS_APP_KEY = " + (propAppKey != null ? propAppKey.substring(0, Math.min(8, propAppKey.length())) + "***" : "null"));
            System.out.println("   当前 this.appKey = " + (this.appKey != null ? this.appKey.substring(0, Math.min(8, this.appKey.length())) + "***" : "null"));

            // 优先使用环境变量，然后是系统属性
            String newAppKey = null;
            if (envAppKey != null && !envAppKey.isEmpty()) {
                newAppKey = envAppKey;
                System.out.println("   ✅ 使用环境变量值");
            } else if (propAppKey != null && !propAppKey.isEmpty()) {
                newAppKey = propAppKey;
                System.out.println("   ✅ 使用系统属性值");
            }

            if (newAppKey != null) {
                this.appKey = newAppKey;
                System.out.println("   ✅ 更新 this.appKey = " + newAppKey.substring(0, Math.min(8, newAppKey.length())) + "***");
            } else {
                System.out.println("   ❌ 所有来源都为空，保持原值");
            }

            System.out.println("   🔑 返回值 = " + (this.appKey != null ? this.appKey.substring(0, Math.min(8, this.appKey.length())) + "***" : "null"));
            return appKey;
        }
        
        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }
        
        public String getRegionId() {
            return regionId;
        }
        
        public void setRegionId(String regionId) {
            this.regionId = regionId;
        }
        
        public String getDomain() {
            return domain;
        }
        
        public void setDomain(String domain) {
            this.domain = domain;
        }
    }
    
    // OSS配置内部类
    public static class OssConfig {
        private String endpoint;
        private String bucketName;
        
        public String getEndpoint() {
            return endpoint;
        }
        
        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }
        
        public String getBucketName() {
            return bucketName;
        }
        
        public void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }
    }
} 