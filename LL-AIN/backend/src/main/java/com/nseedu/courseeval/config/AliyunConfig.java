package com.nseedu.courseeval.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "aliyun")
public class AliyunConfig {

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Value("${ALIYUN_ACCESS_KEY_ID:}")
    private String accessKeyId;

    @Value("${ALIYUN_ACCESS_KEY_SECRET:}")
    private String accessKeySecret;

    private NlsConfig nls;
    private OssConfig oss;
    
    // 构造函数
    public AliyunConfig() {
        this.nls = new NlsConfig();
        this.oss = new OssConfig();

        // 从系统属性中读取配置
        loadFromSystemProperties();
    }

    /**
     * 从环境变量中加载配置
     * 确保在EnvironmentConfig加载环境变量之后执行
     */
    @PostConstruct
    private void loadFromSystemProperties() {
        System.out.println("🔧 AliyunConfig @PostConstruct 开始加载环境变量...");

        // 确保EnvironmentConfig已经加载了环境变量
        if (environmentConfig != null) {
            System.out.println("✅ EnvironmentConfig 已注入，环境变量应该已加载");
        }

        String accessKeyId = System.getenv("ALIYUN_ACCESS_KEY_ID");
        if (accessKeyId != null && !accessKeyId.isEmpty()) {
            this.accessKeyId = accessKeyId;
            System.out.println("✅ 加载 ALIYUN_ACCESS_KEY_ID: " + accessKeyId.substring(0, 8) + "***");
        }

        String accessKeySecret = System.getenv("ALIYUN_ACCESS_KEY_SECRET");
        if (accessKeySecret != null && !accessKeySecret.isEmpty()) {
            this.accessKeySecret = accessKeySecret;
            System.out.println("✅ 加载 ALIYUN_ACCESS_KEY_SECRET: ***");
        }

        String nlsAppKey = System.getenv("ALIYUN_NLS_APP_KEY");
        if (nlsAppKey != null && !nlsAppKey.isEmpty()) {
            this.nls.appKey = nlsAppKey;
            System.out.println("✅ 加载 ALIYUN_NLS_APP_KEY: " + nlsAppKey.substring(0, 8) + "***");
        } else {
            System.out.println("❌ ALIYUN_NLS_APP_KEY 为空或未设置");
            System.out.println("🔍 当前环境变量 ALIYUN_NLS_APP_KEY = " + System.getenv("ALIYUN_NLS_APP_KEY"));
        }

        String nlsDomain = System.getenv("ALIYUN_NLS_DOMAIN");
        if (nlsDomain != null && !nlsDomain.isEmpty()) {
            this.nls.domain = nlsDomain;
            System.out.println("✅ 加载 ALIYUN_NLS_DOMAIN: " + nlsDomain);
        }

        String nlsRegionId = System.getenv("ALIYUN_NLS_REGION_ID");
        if (nlsRegionId != null && !nlsRegionId.isEmpty()) {
            this.nls.regionId = nlsRegionId;
            System.out.println("✅ 加载 ALIYUN_NLS_REGION_ID: " + nlsRegionId);
        }

        System.out.println("🔧 AliyunConfig @PostConstruct 完成");
    }
    
    // Getters and Setters
    public String getAccessKeyId() {
        return accessKeyId;
    }
    
    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }
    
    public String getAccessKeySecret() {
        return accessKeySecret;
    }
    
    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
    
    public NlsConfig getNls() {
        return nls;
    }
    
    public void setNls(NlsConfig nls) {
        this.nls = nls;
    }
    
    public OssConfig getOss() {
        return oss;
    }
    
    public void setOss(OssConfig oss) {
        this.oss = oss;
    }
    
    // NLS配置内部类
    public static class NlsConfig {
        @Value("${ALIYUN_NLS_APP_KEY:}")
        private String appKey;

        @Value("${ALIYUN_NLS_REGION_ID:cn-shanghai}")
        private String regionId;

        @Value("${ALIYUN_NLS_DOMAIN:filetrans.cn-shanghai.aliyuncs.com}")
        private String domain;
        
        public String getAppKey() {
            return appKey;
        }
        
        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }
        
        public String getRegionId() {
            return regionId;
        }
        
        public void setRegionId(String regionId) {
            this.regionId = regionId;
        }
        
        public String getDomain() {
            return domain;
        }
        
        public void setDomain(String domain) {
            this.domain = domain;
        }
    }
    
    // OSS配置内部类
    public static class OssConfig {
        private String endpoint;
        private String bucketName;
        
        public String getEndpoint() {
            return endpoint;
        }
        
        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }
        
        public String getBucketName() {
            return bucketName;
        }
        
        public void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }
    }
} 