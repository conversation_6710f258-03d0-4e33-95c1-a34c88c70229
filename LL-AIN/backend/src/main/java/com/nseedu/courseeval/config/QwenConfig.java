package com.nseedu.courseeval.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "qwen")
public class QwenConfig {

    @Value("${QWEN_MODEL_NAME:qwen-vl}")
    private String modelName;

    @Value("${QWEN_API_URL:https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions}")
    private String apiUrl;

    @Value("${QWEN_API_KEY:}")
    private String apiKey;

    @Value("${QWEN_MAX_TOKENS:8000}")
    private Integer maxTokens = 8000;

    @Value("${QWEN_TEMPERATURE:0.7}")
    private Double temperature = 0.7;

    @Value("${QWEN_TIMEOUT_SECONDS:120}")
    private Integer timeoutSeconds = 120;

    @Value("${QWEN_MAX_RETRIES:3}")
    private Integer maxRetries = 3;

    @Value("${QWEN_RETRY_DELAY_SECONDS:5}")
    private Integer retryDelaySeconds = 5;

    // 构造函数
    public QwenConfig() {
        loadFromSystemProperties();
    }

    /**
     * 从系统属性中加载配置
     */
    @PostConstruct
    private void loadFromSystemProperties() {
        String apiKey = System.getProperty("QWEN_API_KEY");
        if (apiKey != null && !apiKey.isEmpty()) {
            this.apiKey = apiKey;
        }

        String apiUrl = System.getProperty("QWEN_API_URL");
        if (apiUrl != null && !apiUrl.isEmpty()) {
            this.apiUrl = apiUrl;
        }

        String modelName = System.getProperty("QWEN_MODEL_NAME");
        if (modelName != null && !modelName.isEmpty()) {
            this.modelName = modelName;
        }
    }

    // Getters and Setters
    public String getModelName() {
        return modelName;
    }
    
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }
    
    public String getApiUrl() {
        return apiUrl;
    }
    
    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }
    
    public String getApiKey() {
        return apiKey;
    }
    
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
    
    public Integer getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    public Integer getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(Integer maxRetries) {
        this.maxRetries = maxRetries;
    }
    
    public Integer getRetryDelaySeconds() {
        return retryDelaySeconds;
    }
    
    public void setRetryDelaySeconds(Integer retryDelaySeconds) {
        this.retryDelaySeconds = retryDelaySeconds;
    }
} 