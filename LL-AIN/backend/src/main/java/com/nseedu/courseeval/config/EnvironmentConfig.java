package com.nseedu.courseeval.config;

import com.nseedu.courseeval.service.ApiConfigurationService;
import io.github.cdimascio.dotenv.Dotenv;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

@Configuration
public class EnvironmentConfig {

    @Autowired(required = false)
    private ApiConfigurationService apiConfigurationService;

    @PostConstruct
    public void loadEnvironmentVariables() {
        try {
            // 加载 .env 文件 - 优先查找项目根目录
            Dotenv dotenv = null;

            // 尝试从项目根目录加载（LL-AIN/.env）
            try {
                dotenv = Dotenv.configure()
                        .directory("../")  // 上级目录（LL-AIN/）
                        .ignoreIfMalformed()
                        .ignoreIfMissing()
                        .load();
                System.out.println("📁 从项目根目录加载 .env 文件");
            } catch (Exception e) {
                System.out.println("⚠️ 项目根目录未找到 .env 文件，尝试当前目录");

                // 降级到当前目录
                dotenv = Dotenv.configure()
                        .directory("./")  // 当前目录（backend/）
                        .ignoreIfMalformed()
                        .ignoreIfMissing()
                        .load();
            }

            // 将 .env 文件中的变量设置为系统属性和环境变量
            dotenv.entries().forEach(entry -> {
                String key = entry.getKey();
                String value = entry.getValue();

                // 设置系统属性
                System.setProperty(key, value);

                // 设置环境变量（通过反射）
                try {
                    setEnvironmentVariable(key, value);
                } catch (Exception e) {
                    // 忽略设置环境变量失败的错误
                }

                System.out.println("🔧 设置环境变量: " + key + " = " + (key.contains("KEY") || key.contains("SECRET") ? "***" : value));
            });

            System.out.println("✅ 环境变量加载完成");
            System.out.println("🤖 AI功能状态: " + System.getProperty("AI_ENABLED", "false"));

            // 验证关键配置
            validateKeyConfigurations();

            // 延迟检查API配置（等待其他Bean初始化完成）
            new Thread(() -> {
                try {
                    Thread.sleep(3000); // 等待3秒
                    if (apiConfigurationService != null) {
                        apiConfigurationService.printConfigurationStatus();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();

        } catch (Exception e) {
            System.err.println("⚠️ 环境变量加载失败: " + e.getMessage());
            System.err.println("将使用默认配置继续运行");
        }
    }

    /**
     * 设置环境变量（通过反射）
     */
    private void setEnvironmentVariable(String key, String value) throws Exception {
        try {
            Class<?> processEnvironmentClass = Class.forName("java.lang.ProcessEnvironment");
            java.lang.reflect.Field theEnvironmentField = processEnvironmentClass.getDeclaredField("theEnvironment");
            theEnvironmentField.setAccessible(true);
            java.util.Map<String, String> env = (java.util.Map<String, String>) theEnvironmentField.get(null);
            env.put(key, value);

            java.lang.reflect.Field theCaseInsensitiveEnvironmentField = processEnvironmentClass.getDeclaredField("theCaseInsensitiveEnvironment");
            theCaseInsensitiveEnvironmentField.setAccessible(true);
            java.util.Map<String, String> cienv = (java.util.Map<String, String>) theCaseInsensitiveEnvironmentField.get(null);
            cienv.put(key, value);
        } catch (Exception e) {
            // 在某些JVM版本中可能失败，这是正常的
        }
    }

    /**
     * 验证关键配置
     */
    private void validateKeyConfigurations() {
        System.out.println("🔍 验证关键配置:");

        String[] keyConfigs = {
            "ALIYUN_ACCESS_KEY_ID",
            "ALIYUN_ACCESS_KEY_SECRET",
            "ALIYUN_NLS_APP_KEY",
            "ALIYUN_NLS_DOMAIN",
            "QWEN_API_KEY",
            "QWEN_API_URL"
        };

        for (String config : keyConfigs) {
            String value = System.getProperty(config);
            if (value != null && !value.isEmpty() && !value.startsWith("${")) {
                System.out.println("   ✅ " + config + ": " + (config.contains("KEY") || config.contains("SECRET") ? "***" : value));
            } else {
                System.out.println("   ❌ " + config + ": 未配置或配置错误");
            }
        }
    }
}
