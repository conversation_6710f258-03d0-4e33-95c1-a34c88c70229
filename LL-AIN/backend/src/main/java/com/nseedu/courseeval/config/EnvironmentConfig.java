package com.nseedu.courseeval.config;

import com.nseedu.courseeval.service.ApiConfigurationService;
import io.github.cdimascio.dotenv.Dotenv;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

@Configuration
public class EnvironmentConfig {

    @Autowired(required = false)
    private ApiConfigurationService apiConfigurationService;

    @PostConstruct
    public void loadEnvironmentVariables() {
        try {
            // 加载 .env 文件 - 优先查找项目根目录
            Dotenv dotenv = null;

            // 尝试从项目根目录加载（LL-AIN/.env）
            try {
                dotenv = Dotenv.configure()
                        .directory("../")  // 上级目录（LL-AIN/）
                        .ignoreIfMalformed()
                        .ignoreIfMissing()
                        .load();
                System.out.println("📁 从项目根目录加载 .env 文件");
            } catch (Exception e) {
                System.out.println("⚠️ 项目根目录未找到 .env 文件，尝试当前目录");

                // 降级到当前目录
                dotenv = Dotenv.configure()
                        .directory("./")  // 当前目录（backend/）
                        .ignoreIfMalformed()
                        .ignoreIfMissing()
                        .load();
            }

            // 将 .env 文件中的变量设置为系统属性
            dotenv.entries().forEach(entry -> {
                String key = entry.getKey();
                String value = entry.getValue();
                
                // 只有当系统属性不存在时才设置（优先使用系统环境变量）
                if (System.getProperty(key) == null && System.getenv(key) == null) {
                    System.setProperty(key, value);
                }
            });

            System.out.println("✅ 环境变量加载完成");
            System.out.println("🤖 AI功能状态: " + System.getProperty("AI_ENABLED", "false"));

            // 延迟检查API配置（等待其他Bean初始化完成）
            new Thread(() -> {
                try {
                    Thread.sleep(3000); // 等待3秒
                    if (apiConfigurationService != null) {
                        apiConfigurationService.printConfigurationStatus();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();

        } catch (Exception e) {
            System.err.println("⚠️ 环境变量加载失败: " + e.getMessage());
            System.err.println("将使用默认配置继续运行");
        }
    }
}
