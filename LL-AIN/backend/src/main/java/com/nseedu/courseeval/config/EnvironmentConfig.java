package com.nseedu.courseeval.config;

import io.github.cdimascio.dotenv.Dotenv;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class EnvironmentConfig {

    @PostConstruct
    public void loadEnvironmentVariables() {
        try {
            // 加载 .env 文件
            Dotenv dotenv = Dotenv.configure()
                    .directory("./")  // 在项目根目录查找 .env 文件
                    .ignoreIfMalformed()
                    .ignoreIfMissing()
                    .load();

            // 将 .env 文件中的变量设置为系统属性
            dotenv.entries().forEach(entry -> {
                String key = entry.getKey();
                String value = entry.getValue();
                
                // 只有当系统属性不存在时才设置（优先使用系统环境变量）
                if (System.getProperty(key) == null && System.getenv(key) == null) {
                    System.setProperty(key, value);
                }
            });

            System.out.println("✅ 环境变量加载完成");
            System.out.println("🤖 AI功能状态: " + System.getProperty("AI_ENABLED", "false"));
            
        } catch (Exception e) {
            System.err.println("⚠️ 环境变量加载失败: " + e.getMessage());
            System.err.println("将使用默认配置继续运行");
        }
    }
}
