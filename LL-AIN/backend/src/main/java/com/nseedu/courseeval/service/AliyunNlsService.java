package com.nseedu.courseeval.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.nseedu.courseeval.config.AliyunConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

@Service
public class AliyunNlsService {

    // 阿里云录音文件识别闲时版常量
    private static final String REGION_ID = "cn-shanghai";
    private static final String ENDPOINT_NAME = "cn-shanghai";
    private static final String PRODUCT = "SpeechFileTranscriberLite";
    private static final String DOMAIN = "speechfiletranscriberlite.cn-shanghai.aliyuncs.com";
    private static final String API_VERSION = "2021-12-21";
    private static final String POST_REQUEST_ACTION = "SubmitTask";
    private static final String GET_REQUEST_ACTION = "GetTaskResult";

    // 请求参数键
    private static final String KEY_APP_KEY = "appkey";
    private static final String KEY_FILE_LINK = "file_link";
    private static final String KEY_ENABLE_WORDS = "enable_words";
    private static final String KEY_TASK = "Task";
    private static final String KEY_TASK_ID = "TaskId";
    private static final String KEY_STATUS_TEXT = "StatusText";
    private static final String KEY_RESULT = "Result";

    // 状态值
    private static final String STATUS_SUCCESS = "SUCCESS";
    private static final String STATUS_RUNNING = "RUNNING";
    private static final String STATUS_QUEUEING = "QUEUEING";

    @Autowired
    private AliyunConfig aliyunConfig;

    private IAcsClient acsClient;

    /**
     * 初始化阿里云客户端
     */
    private void initializeAcsClient() {
        if (acsClient == null) {
            try {
                // 设置endpoint
                DefaultProfile.addEndpoint(ENDPOINT_NAME, REGION_ID, PRODUCT, DOMAIN);

                // 创建DefaultAcsClient实例并初始化
                DefaultProfile profile = DefaultProfile.getProfile(
                    REGION_ID,
                    aliyunConfig.getAccessKeyId(),
                    aliyunConfig.getAccessKeySecret()
                );
                acsClient = new DefaultAcsClient(profile);

                System.out.println("✅ 阿里云ACS客户端初始化成功");
            } catch (ClientException e) {
                System.err.println("❌ 阿里云ACS客户端初始化失败: " + e.getMessage());
                throw new RuntimeException("阿里云ACS客户端初始化失败", e);
            }
        }
    }

    /**
     * 提交语音转写任务
     * @param audioFilePath 音频文件路径
     * @return 任务ID
     */
    public String submitTranscriptionTask(String audioFilePath) {
        try {
            // 验证配置
            validateConfiguration();

            // 初始化客户端
            initializeAcsClient();

            System.out.println("📤 提交语音转写任务到阿里云NLS...");
            System.out.println("🔧 NLS配置验证:");
            System.out.println("   AccessKeyId: " + (aliyunConfig.getAccessKeyId() != null ? "✅" : "❌"));
            System.out.println("   AccessKeySecret: " + (aliyunConfig.getAccessKeySecret() != null ? "✅" : "❌"));
            System.out.println("   NLS AppKey: " + (aliyunConfig.getNls().getAppKey() != null ? "✅" : "❌"));
            System.out.println("   NLS Domain: " + (aliyunConfig.getNls().getDomain() != null ? "✅" : "❌"));

            if (aliyunConfig.getAccessKeyId() == null || aliyunConfig.getAccessKeySecret() == null ||
                aliyunConfig.getNls().getAppKey() == null) {
                throw new RuntimeException("阿里云配置不完整");
            }

            System.out.println("✅ NLS配置验证通过");

            // 上传音频文件到OSS
            String fileUrl = uploadAudioToOSS(audioFilePath);
            System.out.println("📤 音频文件已上传到OSS: " + fileUrl);

            // 提交转写任务
            String taskId = submitTask(fileUrl);
            System.out.println("✅ 转写任务提交成功，任务ID: " + taskId);

            return taskId;

        } catch (Exception e) {
            System.err.println("❌ 提交语音转写任务失败: " + e.getMessage());
            throw new RuntimeException("语音转写失败", e);
        }
    }

    /**
     * 提交转写任务
     */
    private String submitTask(String fileUrl) throws ClientException {
        // 创建CommonRequest，设置请求参数
        CommonRequest postRequest = new CommonRequest();
        postRequest.setDomain(DOMAIN);
        postRequest.setVersion(API_VERSION);
        postRequest.setAction(POST_REQUEST_ACTION);
        postRequest.setProduct(PRODUCT);

        // 设置录音文件识别请求参数，以JSON字符串的格式设置到请求Body中
        JSONObject taskObject = new JSONObject();
        taskObject.put(KEY_APP_KEY, aliyunConfig.getNls().getAppKey());
        taskObject.put(KEY_FILE_LINK, fileUrl);
        taskObject.put(KEY_ENABLE_WORDS, false);  // 不需要词信息

        String task = taskObject.toJSONString();
        System.out.println("📝 请求JSON: " + task);

        postRequest.putBodyParameter(KEY_TASK, task);
        postRequest.setMethod(MethodType.POST);

        // 提交录音文件识别请求
        CommonResponse postResponse = acsClient.getCommonResponse(postRequest);
        System.out.println("🌐 NLS响应状态: " + postResponse.getHttpStatus());
        System.out.println("🌐 NLS响应内容: " + postResponse.getData());

        if (postResponse.getHttpStatus() == 200) {
            JSONObject result = JSONObject.parseObject(postResponse.getData());
            String statusText = result.getString(KEY_STATUS_TEXT);
            if (STATUS_SUCCESS.equals(statusText)) {
                return result.getString(KEY_TASK_ID);
            } else {
                throw new RuntimeException("转写任务提交失败: " + result.toJSONString());
            }
        } else {
            throw new RuntimeException("转写任务提交失败: HTTP " + postResponse.getHttpStatus() + " - " + postResponse.getData());
        }
    }

    /**
     * 轮询获取转写结果
     */
    public TranscriptionResult pollTaskResult(String taskId) throws Exception {
        System.out.println("⏳ 开始轮询转写结果，任务ID: " + taskId);

        // 创建CommonRequest，设置任务ID
        CommonRequest getRequest = new CommonRequest();
        getRequest.setDomain(DOMAIN);
        getRequest.setVersion(API_VERSION);
        getRequest.setAction(GET_REQUEST_ACTION);
        getRequest.setProduct(PRODUCT);
        getRequest.putQueryParameter(KEY_TASK_ID, taskId);
        getRequest.setMethod(MethodType.GET);

        // 轮询识别结果
        int maxRetries = 60; // 最多轮询60次（5分钟）
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                CommonResponse getResponse = acsClient.getCommonResponse(getRequest);

                if (getResponse.getHttpStatus() != 200) {
                    System.err.println("❌ 识别结果查询失败: HTTP " + getResponse.getHttpStatus());
                    break;
                }

                JSONObject result = JSONObject.parseObject(getResponse.getData());
                String statusText = result.getString(KEY_STATUS_TEXT);

                System.out.println("📊 轮询状态: " + statusText + " (第" + (retryCount + 1) + "次)");

                if (STATUS_RUNNING.equals(statusText) || STATUS_QUEUEING.equals(statusText)) {
                    // 继续轮询
                    Thread.sleep(5000); // 等待5秒
                    retryCount++;
                } else if (STATUS_SUCCESS.equals(statusText)) {
                    // 成功获取结果
                    String resultText = result.getString(KEY_RESULT);
                    if (resultText == null || resultText.trim().isEmpty()) {
                        resultText = ""; // 空结果
                    }
                    System.out.println("✅ 转写成功完成");
                    return new TranscriptionResult(resultText);
                } else {
                    // 失败
                    throw new RuntimeException("转写失败: " + result.toJSONString());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("轮询被中断", e);
            } catch (ClientException e) {
                throw new RuntimeException("轮询请求失败", e);
            }
        }

        throw new RuntimeException("转写任务超时");
    }

    /**
     * 上传音频文件到OSS
     */
    private String uploadAudioToOSS(String audioFilePath) throws IOException {
        System.out.println("🔧 OSS配置信息:");
        System.out.println("   Endpoint: " + aliyunConfig.getOss().getEndpoint());
        System.out.println("   Bucket: " + aliyunConfig.getOss().getBucketName());
        System.out.println("   AccessKeyId: " + aliyunConfig.getAccessKeyId().substring(0, 8) + "***");
        System.out.println("   AccessKeySecret: ***");

        // 创建OSS客户端
        OSS ossClient = new OSSClientBuilder().build(
            aliyunConfig.getOss().getEndpoint(),
            aliyunConfig.getAccessKeyId(),
            aliyunConfig.getAccessKeySecret()
        );

        try {
            // 生成唯一的文件名
            String fileName = "audio/" + UUID.randomUUID().toString() + "_" + new File(audioFilePath).getName();

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                aliyunConfig.getOss().getBucketName(),
                fileName,
                new File(audioFilePath)
            );

            ossClient.putObject(putObjectRequest);

            // 生成访问URL
            String fileUrl = "https://" + aliyunConfig.getOss().getBucketName() + "." + aliyunConfig.getOss().getEndpoint() + "/" + fileName;

            System.out.println("✅ 文件上传成功: " + fileName);
            System.out.println("🌐 访问URL: " + fileUrl);

            return fileUrl;

        } finally {
            ossClient.shutdown();
        }
    }

    /**
     * 验证配置
     */
    private void validateConfiguration() {
        System.out.println("🔍 开始验证阿里云配置...");

        // 直接使用配置类的值，因为它已经正确加载了.env文件
        String accessKeyId = aliyunConfig.getAccessKeyId();
        System.out.println("   AccessKeyId: " + (accessKeyId != null && !accessKeyId.trim().isEmpty() ? accessKeyId.substring(0, Math.min(8, accessKeyId.length())) + "***" : "null"));
        if (accessKeyId == null || accessKeyId.trim().isEmpty()) {
            throw new RuntimeException("阿里云AccessKeyId未配置");
        }

        String accessKeySecret = aliyunConfig.getAccessKeySecret();
        System.out.println("   AccessKeySecret: " + (accessKeySecret != null && !accessKeySecret.trim().isEmpty() ? accessKeySecret.substring(0, Math.min(8, accessKeySecret.length())) + "***" : "null"));
        if (accessKeySecret == null || accessKeySecret.trim().isEmpty()) {
            throw new RuntimeException("阿里云AccessKeySecret未配置");
        }

        String appKey = aliyunConfig.getNls().getAppKey();
        System.out.println("   NLS AppKey: " + (appKey != null && !appKey.trim().isEmpty() ? appKey.substring(0, Math.min(8, appKey.length())) + "***" : "null"));
        if (appKey == null || appKey.trim().isEmpty()) {
            throw new RuntimeException("阿里云NLS AppKey未配置");
        }

        String endpoint = aliyunConfig.getOss().getEndpoint();
        System.out.println("   OSS Endpoint: " + (endpoint != null ? endpoint : "null"));
        if (endpoint == null || endpoint.trim().isEmpty()) {
            throw new RuntimeException("阿里云OSS Endpoint未配置");
        }

        String bucketName = aliyunConfig.getOss().getBucketName();
        System.out.println("   OSS BucketName: " + (bucketName != null ? bucketName : "null"));
        if (bucketName == null || bucketName.trim().isEmpty()) {
            throw new RuntimeException("阿里云OSS Bucket名称未配置");
        }

        System.out.println("✅ 阿里云配置验证通过");
    }

    /**
     * 转写结果类
     */
    public static class TranscriptionResult {
        private final String text;

        public TranscriptionResult(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }
}