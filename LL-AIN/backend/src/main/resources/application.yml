server:
  port: 8080

spring:
  security:
    enabled: false   # 开发阶段先关闭
  
  # 数据库配置
  datasource:
    url: *************************************************************************************************************
    username: root
    password: Nseedu2025
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: none  # 不自动修改表结构，使用 Prisma 管理
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB

# 自定义文件上传配置
file:
  upload-dir: /Users/<USER>/Desktop/MyProject/LL-AIN/backend/uploads/videos 

# AI评估开关
ai:
  enabled: ${AI_ENABLED:true}  # true=真实AI服务，false=智能模拟

# 阿里云服务配置
aliyun:
  access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}

  # NLS语音识别
  nls:
    app-key: ${ALIYUN_NLS_APP_KEY:}
    region-id: ${ALIYUN_NLS_REGION_ID:cn-shanghai}
    domain: ${ALIYUN_NLS_DOMAIN:filetrans.cn-shanghai.aliyuncs.com}

  # OSS存储
  oss:
    endpoint: ${ALIYUN_OSS_ENDPOINT:oss-cn-shanghai.aliyuncs.com}
    bucket-name: ${ALIYUN_OSS_BUCKET_NAME:ai-pingke}

# Qwen大模型
qwen:
  model-name: ${QWEN_MODEL_NAME:qwen-vl}
  api-url: ${QWEN_API_URL:https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions}
  api-key: ${QWEN_API_KEY:}
  max-tokens: ${QWEN_MAX_TOKENS:8000}
  temperature: ${QWEN_TEMPERATURE:0.7}
  timeout-seconds: ${QWEN_TIMEOUT_SECONDS:120}
  max-retries: ${QWEN_MAX_RETRIES:3}
  retry-delay-seconds: ${QWEN_RETRY_DELAY_SECONDS:5}