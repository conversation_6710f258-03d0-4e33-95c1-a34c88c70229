[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------------< com.nseedu:courseeval >------------------------
[INFO] Building Course Evaluation System 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ courseeval >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ courseeval ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] Copying 0 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.11.0:compile (default-compile) @ courseeval ---
[INFO] Changes detected - recompiling the module! :source
[INFO] Compiling 45 source files with javac [debug release 17] to target/classes
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ courseeval ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Desktop/MyProject/LL-AIN/backend/src/test/resources
[INFO] 
[INFO] --- compiler:3.11.0:testCompile (default-testCompile) @ courseeval ---
[INFO] Changes detected - recompiling the module! :dependency
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ courseeval <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ courseeval ---
[INFO] Attaching agents: []
Standard Commons Logging discovery in action with spring-jcl: please remove commons-logging.jar from classpath in order to avoid potential conflicts

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-17T18:41:08.678+08:00  INFO 90898 --- [           main] com.nseedu.courseeval.Application        : Starting Application using Java 17.0.15 with PID 90898 (/Users/<USER>/Desktop/MyProject/LL-AIN/backend/target/classes started by nseedu in /Users/<USER>/Desktop/MyProject/LL-AIN/backend)
2025-07-17T18:41:08.679+08:00  INFO 90898 --- [           main] com.nseedu.courseeval.Application        : No active profile set, falling back to 1 default profile: "default"
2025-07-17T18:41:09.216+08:00  INFO 90898 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-17T18:41:09.281+08:00  INFO 90898 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 57 ms. Found 9 JPA repository interfaces.
2025-07-17T18:41:09.739+08:00  INFO 90898 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-17T18:41:09.748+08:00  INFO 90898 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-17T18:41:09.749+08:00  INFO 90898 --- [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-07-17T18:41:09.808+08:00  INFO 90898 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-17T18:41:09.809+08:00  INFO 90898 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1098 ms
Standard Commons Logging discovery in action with spring-jcl: please remove commons-logging.jar from classpath in order to avoid potential conflicts
2025-07-17T18:41:09.977+08:00  INFO 90898 --- [           main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-17T18:41:10.052+08:00  INFO 90898 --- [           main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-07-17T18:41:10.086+08:00  INFO 90898 --- [           main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-17T18:41:10.323+08:00  INFO 90898 --- [           main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-17T18:41:10.349+08:00  INFO 90898 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-17T18:41:10.862+08:00  INFO 90898 --- [           main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5599b5bb
2025-07-17T18:41:10.864+08:00  INFO 90898 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-17T18:41:10.928+08:00  WARN 90898 --- [           main] o.h.e.j.e.i.JdbcEnvironmentInitiator     : HHH000339: Could not obtain connection metadata: java.sql.SQLSyntaxErrorException: Unknown column 'RESERVED' in 'WHERE'
2025-07-17T18:41:10.950+08:00  WARN 90898 --- [           main] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-17T18:41:11.852+08:00  INFO 90898 --- [           main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-17T18:41:11.854+08:00  INFO 90898 --- [           main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-17T18:41:12.082+08:00  INFO 90898 --- [           main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-17T18:41:12.806+08:00  WARN 90898 --- [           main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-17T18:41:13.131+08:00  INFO 90898 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-07-17T18:41:13.140+08:00  INFO 90898 --- [           main] com.nseedu.courseeval.Application        : Started Application in 4.838 seconds (process running for 5.137)
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T18:41:17.141+08:00  INFO 90898 --- [nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17T18:41:17.142+08:00  INFO 90898 --- [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-17T18:41:17.143+08:00  INFO 90898 --- [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T18:46:45.738+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m34s770ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T18:46:54.773+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5599b5bb (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T20:42:58.130+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@500a6f76 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T20:42:58.133+08:00  WARN 90898 --- [   scheduling-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 08003
2025-07-17T20:42:58.133+08:00 ERROR 90898 --- [   scheduling-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : HikariPool-1 - Connection is not available, request timed out after 6968375ms.
2025-07-17T20:42:58.133+08:00 ERROR 90898 --- [   scheduling-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : No operations allowed after connection closed.
❌ 定时任务执行失败: Unable to acquire JDBC Connection [HikariPool-1 - Connection is not available, request timed out after 6968375ms.] [n/a]
org.springframework.dao.DataAccessResourceFailureException: Unable to acquire JDBC Connection [HikariPool-1 - Connection is not available, request timed out after 6968375ms.] [n/a]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:274)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:135)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy120.findByStatusAndCreatedAtBefore(Unknown Source)
	at com.nseedu.courseeval.scheduler.EvaluationTaskScheduler.processEvaluationTasks(EvaluationTaskScheduler.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:499)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection [HikariPool-1 - Connection is not available, request timed out after 6968375ms.] [n/a]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:51)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:116)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:143)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.connection(StatementPreparerImpl.java:51)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$5.doPrepare(StatementPreparerImpl.java:150)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:177)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:152)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.lambda$list$0(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:227)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:200)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:109)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$DeferredQueryInvocationHandler.invoke(SharedEntityManagerCreator.java:405)
	at jdk.proxy2/jdk.proxy2.$Proxy138.getResultList(Unknown Source)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$CollectionExecution.doExecute(JpaQueryExecution.java:129)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:149)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 23 more
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 6968375ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:181)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:146)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:38)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:113)
	... 69 more
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2451)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:561)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionDead(PoolBase.java:168)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:170)
	... 74 more
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:753)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:559)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2447)
	... 77 more
2025-07-17T21:13:55.528+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2h27m9s790ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T21:14:09.644+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2e843bf7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:14:09.645+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2b138378 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:14:09.646+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@8912d5a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:14:09.647+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3f2e7773 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:14:09.647+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@14a12a34 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:14:09.648+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@168b4527 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:14:09.649+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@17715151 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:14:09.649+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5ba83923 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T21:43:19.228+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@55c75235 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.230+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4946c29a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.230+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4dbd3f6e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.231+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5ae8d0d9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.231+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3551774b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.232+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4a7c2169 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.233+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6e1e3107 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.233+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@726c5a05 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T21:43:19.234+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4cb9802c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T22:12:59.480+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@706ee681 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:12:59.481+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7a5d3965 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:12:59.483+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@250a30d7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:12:59.484+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3b86677c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
2025-07-17T22:38:05.179+08:00  WARN 90898 --- [nio-8080-exec-6] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@fb748ba (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.179+08:00  WARN 90898 --- [nio-8080-exec-4] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@57a08ea4 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.179+08:00  WARN 90898 --- [nio-8080-exec-3] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5fcded7b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.179+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@66ca0ca9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.181+08:00  WARN 90898 --- [nio-8080-exec-4] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3b789984 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.181+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5d0de92e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.181+08:00  WARN 90898 --- [nio-8080-exec-3] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@75dddff2 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.181+08:00  WARN 90898 --- [nio-8080-exec-6] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4019577f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:38:05.181+08:00  WARN 90898 --- [nio-8080-exec-4] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@497a333f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T22:54:15.992+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m19s850ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-17T22:54:28.044+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3600d669 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.045+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@43821193 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.048+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@57138ed9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.049+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5dfd4918 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.050+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@ea0c5bd (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.050+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6cacf5ea (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.051+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2782dbe0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.051+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1080e381 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.052+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@18055101 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T22:54:28.053+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@130de44e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
2025-07-17T23:11:47.541+08:00  WARN 90898 --- [nio-8080-exec-2] com.zaxxer.hikari.pool.ProxyConnection   : HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@43042064 marked as broken because of SQLSTATE(08S01), ErrorCode(0)

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52) ~[HikariCP-5.0.1.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java) ~[HikariCP-5.0.1.jar:na]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:240) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:186) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdLoadPlan.load(SingleIdLoadPlan.java:145) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdLoadPlan.load(SingleIdLoadPlan.java:117) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdEntityLoaderStandardImpl.load(SingleIdEntityLoaderStandardImpl.java:75) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.doLoad(AbstractEntityPersister.java:3673) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.load(AbstractEntityPersister.java:3662) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadFromDatasource(DefaultLoadEventListener.java:591) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadFromCacheOrDatasource(DefaultLoadEventListener.java:577) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.load(DefaultLoadEventListener.java:547) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.doLoad(DefaultLoadEventListener.java:531) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.load(DefaultLoadEventListener.java:206) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadWithRegularProxy(DefaultLoadEventListener.java:286) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.proxyOrLoad(DefaultLoadEventListener.java:241) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.doOnLoad(DefaultLoadEventListener.java:110) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.onLoad(DefaultLoadEventListener.java:67) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:138) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.fireLoadNoChecks(SessionImpl.java:1234) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.fireLoad(SessionImpl.java:1222) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.load(IdentifierLoadAccessImpl.java:209) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.doLoad(IdentifierLoadAccessImpl.java:160) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.lambda$load$1(IdentifierLoadAccessImpl.java:149) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.perform(IdentifierLoadAccessImpl.java:112) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.load(IdentifierLoadAccessImpl.java:149) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.find(SessionImpl.java:2430) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.find(SessionImpl.java:2401) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at jdk.internal.reflect.GeneratedMethodAccessor50.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy112.find(Unknown Source) ~[na:na]
	at jdk.internal.reflect.GeneratedMethodAccessor50.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy112.find(Unknown Source) ~[na:na]
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById(SimpleJpaRepository.java:313) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249) ~[spring-aop-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy120.findById(Unknown Source) ~[na:na]
	at com.nseedu.courseeval.service.TaskService.getTaskStatus(TaskService.java:88) ~[classes/:na]
	at com.nseedu.courseeval.controller.TaskController.getTaskStatus(TaskController.java:47) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.
	at jdk.internal.reflect.GeneratedConstructorAccessor45.newInstance(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1052) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:657) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 131 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 136 common frames omitted

2025-07-17T23:11:47.677+08:00  WARN 90898 --- [nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 08S01
2025-07-17T23:11:47.678+08:00 ERROR 90898 --- [nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.
2025-07-17T23:11:47.679+08:00  INFO 90898 --- [nio-8080-exec-2] o.h.e.internal.DefaultLoadEventListener  : HHH000327: Error performing load command

org.hibernate.exception.JDBCConnectionException: JDBC exception executing SQL [select et1_0.id,et1_0.course_id,et1_0.created_at,et1_0.error_msg,et1_0.evaluator_id,et1_0.finished_at,et1_0.progress,et1_0.rubric_version_id,et1_0.started_at,et1_0.status from evaluation_task et1_0 where et1_0.id=?] [Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:100) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:258) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:186) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdLoadPlan.load(SingleIdLoadPlan.java:145) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdLoadPlan.load(SingleIdLoadPlan.java:117) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdEntityLoaderStandardImpl.load(SingleIdEntityLoaderStandardImpl.java:75) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.doLoad(AbstractEntityPersister.java:3673) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.load(AbstractEntityPersister.java:3662) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadFromDatasource(DefaultLoadEventListener.java:591) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadFromCacheOrDatasource(DefaultLoadEventListener.java:577) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.load(DefaultLoadEventListener.java:547) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.doLoad(DefaultLoadEventListener.java:531) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.load(DefaultLoadEventListener.java:206) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadWithRegularProxy(DefaultLoadEventListener.java:286) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.proxyOrLoad(DefaultLoadEventListener.java:241) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.doOnLoad(DefaultLoadEventListener.java:110) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.onLoad(DefaultLoadEventListener.java:67) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:138) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.fireLoadNoChecks(SessionImpl.java:1234) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.fireLoad(SessionImpl.java:1222) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.load(IdentifierLoadAccessImpl.java:209) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.doLoad(IdentifierLoadAccessImpl.java:160) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.lambda$load$1(IdentifierLoadAccessImpl.java:149) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.perform(IdentifierLoadAccessImpl.java:112) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.load(IdentifierLoadAccessImpl.java:149) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.find(SessionImpl.java:2430) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.find(SessionImpl.java:2401) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at jdk.internal.reflect.GeneratedMethodAccessor50.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy112.find(Unknown Source) ~[na:na]
	at jdk.internal.reflect.GeneratedMethodAccessor50.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy112.find(Unknown Source) ~[na:na]
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById(SimpleJpaRepository.java:313) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249) ~[spring-aop-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy120.findById(Unknown Source) ~[na:na]
	at com.nseedu.courseeval.service.TaskService.getTaskStatus(TaskService.java:88) ~[classes/:na]
	at com.nseedu.courseeval.controller.TaskController.getTaskStatus(TaskController.java:47) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52) ~[HikariCP-5.0.1.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java) ~[HikariCP-5.0.1.jar:na]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:240) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 127 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.
	at jdk.internal.reflect.GeneratedConstructorAccessor45.newInstance(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1052) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:657) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 131 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 136 common frames omitted

2025-07-17T23:11:47.698+08:00 ERROR 90898 --- [nio-8080-exec-2] o.s.t.i.TransactionInterceptor           : Application exception overridden by rollback exception

org.hibernate.exception.JDBCConnectionException: JDBC exception executing SQL [select et1_0.id,et1_0.course_id,et1_0.created_at,et1_0.error_msg,et1_0.evaluator_id,et1_0.finished_at,et1_0.progress,et1_0.rubric_version_id,et1_0.started_at,et1_0.status from evaluation_task et1_0 where et1_0.id=?] [Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:100) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:258) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:186) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdLoadPlan.load(SingleIdLoadPlan.java:145) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdLoadPlan.load(SingleIdLoadPlan.java:117) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.ast.internal.SingleIdEntityLoaderStandardImpl.load(SingleIdEntityLoaderStandardImpl.java:75) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.doLoad(AbstractEntityPersister.java:3673) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.load(AbstractEntityPersister.java:3662) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadFromDatasource(DefaultLoadEventListener.java:591) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadFromCacheOrDatasource(DefaultLoadEventListener.java:577) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.load(DefaultLoadEventListener.java:547) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.doLoad(DefaultLoadEventListener.java:531) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.load(DefaultLoadEventListener.java:206) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.loadWithRegularProxy(DefaultLoadEventListener.java:286) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.proxyOrLoad(DefaultLoadEventListener.java:241) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.doOnLoad(DefaultLoadEventListener.java:110) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.internal.DefaultLoadEventListener.onLoad(DefaultLoadEventListener.java:67) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:138) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.fireLoadNoChecks(SessionImpl.java:1234) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.fireLoad(SessionImpl.java:1222) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.load(IdentifierLoadAccessImpl.java:209) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.doLoad(IdentifierLoadAccessImpl.java:160) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.lambda$load$1(IdentifierLoadAccessImpl.java:149) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.perform(IdentifierLoadAccessImpl.java:112) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.loader.internal.IdentifierLoadAccessImpl.load(IdentifierLoadAccessImpl.java:149) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.find(SessionImpl.java:2430) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.SessionImpl.find(SessionImpl.java:2401) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at jdk.internal.reflect.GeneratedMethodAccessor50.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy112.find(Unknown Source) ~[na:na]
	at jdk.internal.reflect.GeneratedMethodAccessor50.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy112.find(Unknown Source) ~[na:na]
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById(SimpleJpaRepository.java:313) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249) ~[spring-aop-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy120.findById(Unknown Source) ~[na:na]
	at com.nseedu.courseeval.service.TaskService.getTaskStatus(TaskService.java:88) ~[classes/:na]
	at com.nseedu.courseeval.controller.TaskController.getTaskStatus(TaskController.java:47) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52) ~[HikariCP-5.0.1.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java) ~[HikariCP-5.0.1.jar:na]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:240) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 127 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 140 milliseconds ago. The last packet sent successfully to the server was 411 milliseconds ago.
	at jdk.internal.reflect.GeneratedConstructorAccessor45.newInstance(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1052) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:657) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 131 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 136 common frames omitted

❌ 查询任务状态失败: Unable to rollback against JDBC Connection
2025-07-17T23:11:47.713+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@785c5a78 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.714+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2c990a54 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.715+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@39652717 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.716+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5742b9bd (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.717+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4d57a6b7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.717+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@29f8dee3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.718+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@39350954 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.718+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7bf8d09e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-17T23:11:47.719+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@beaa348 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T00:32:18.599+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1b78d51d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.601+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7ed9aeaa (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.603+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1358182d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.604+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4ed5e766 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.606+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@56cbe50a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.607+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@65d4bd87 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.609+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@361e4860 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.610+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@57916712 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.612+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5e7b2b5f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:18.614+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1e038d92 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T00:32:28.210+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h15m42s29ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T02:16:56.342+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4b84be10 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.343+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2cbdc398 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.344+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6eeededf (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.344+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@f4992c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.345+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3ff8f87 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.345+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@411c2d63 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.346+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@591c1bb9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.346+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7d85d44f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.347+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2f9ffcf (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:16:56.347+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@222ebdc6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T02:18:40.395+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h46m12s125ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T03:19:46.384+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@70710da6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:46.479+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7bbd2931 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:46.951+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5fca7212 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:47.048+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@272e5870 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:47.099+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@62cfd3eb (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:47.297+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@70298a22 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:47.401+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7c7f3cd7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:47.451+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@294706f0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:47.657+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@350bc8b8 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T03:19:47.827+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@94f3ca1 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T04:20:54.701+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2h2m14s365ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T05:22:03.395+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@467a894e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.402+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6c6179ed (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.402+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5d213dca (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.403+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4f83ffd9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.403+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1a381470 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.404+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7d71b8cb (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.405+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@29ea15c2 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.405+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@79475a11 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.406+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@23864add (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T05:22:03.406+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@53c10986 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:23:10.282+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2h2m15s581ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T06:47:12.399+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@12634773 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.408+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@d946af3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.408+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7d478182 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.409+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@75971986 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.409+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7d04d336 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.410+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5369fd97 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.411+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@12e32de9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.413+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5f754d1a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.413+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@433b8a1 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T06:47:12.414+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@18aa68f5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:24:15.104+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h1m4s820ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T07:48:25.844+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@59f7a95c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.854+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5877c813 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.856+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@67ae907f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.859+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3f7689f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.862+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@497d96c0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.865+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2face8ac (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.866+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3c92a84e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.867+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4198cd3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.868+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@21f7d106 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:25.869+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6956b41c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T07:48:38.848+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=24m23s747ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
2025-07-18T08:06:29.706+08:00  WARN 90898 --- [nio-8080-exec-4] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2f474b73 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.706+08:00  WARN 90898 --- [nio-8080-exec-3] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7c3d4521 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.706+08:00  WARN 90898 --- [io-8080-exec-10] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5fea2ff (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.706+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@672bacd5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.707+08:00  WARN 90898 --- [io-8080-exec-10] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@9dcb3c7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.707+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@38e1c04b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.707+08:00  WARN 90898 --- [nio-8080-exec-3] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7ef364ed (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.707+08:00  WARN 90898 --- [nio-8080-exec-4] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@50ea65e7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:06:29.708+08:00  WARN 90898 --- [io-8080-exec-10] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4a1ef43b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T08:15:30.719+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@61a95e6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.720+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@51501586 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.721+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4cd3bbc0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.721+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1e34a219 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.722+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@26745e34 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.722+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@77598359 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.723+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@26852bc9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.723+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1d1a616a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.723+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@191b0903 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:15:30.723+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@22630232 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
2025-07-18T08:15:36.717+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=8m57s716ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T08:28:25.443+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@41fc2408 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.444+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@108fcefd (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.445+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@799e77db (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.445+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@59e2c084 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.446+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6ad67335 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.446+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@440b0b4 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.447+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@eea2f48 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.447+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@41c328f1 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.447+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@18d5a427 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:25.448+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@19916299 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:28:30.751+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m54s34ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
2025-07-18T08:42:48.518+08:00  WARN 90898 --- [nio-8080-exec-3] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@14e175d3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.518+08:00  WARN 90898 --- [io-8080-exec-10] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6ca5bb76 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.518+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@722132a0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.518+08:00  WARN 90898 --- [nio-8080-exec-4] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@559b3953 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.519+08:00  WARN 90898 --- [io-8080-exec-10] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@439996c7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.519+08:00  WARN 90898 --- [nio-8080-exec-3] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@dc0c176 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.519+08:00  WARN 90898 --- [nio-8080-exec-4] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@372d1782 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.519+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@68852e44 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T08:42:48.519+08:00  WARN 90898 --- [io-8080-exec-10] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5f47bfb3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T09:08:28.984+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=25m28s136ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T09:08:34.175+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4eb411e7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:08:39.179+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@75cc2af0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:08:44.182+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2032d81a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:08:49.183+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2b9a8c15 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:06.047+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3f15a83c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:06.051+08:00  WARN 90898 --- [   scheduling-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 08003
2025-07-18T09:15:06.051+08:00 ERROR 90898 --- [   scheduling-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : HikariPool-1 - Connection is not available, request timed out after 396874ms.
2025-07-18T09:15:06.052+08:00 ERROR 90898 --- [   scheduling-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : No operations allowed after connection closed.
❌ 定时任务执行失败: Unable to acquire JDBC Connection [HikariPool-1 - Connection is not available, request timed out after 396874ms.] [n/a]
org.springframework.dao.DataAccessResourceFailureException: Unable to acquire JDBC Connection [HikariPool-1 - Connection is not available, request timed out after 396874ms.] [n/a]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:274)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:135)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy120.findByStatusAndCreatedAtBefore(Unknown Source)
	at com.nseedu.courseeval.scheduler.EvaluationTaskScheduler.processEvaluationTasks(EvaluationTaskScheduler.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:499)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection [HikariPool-1 - Connection is not available, request timed out after 396874ms.] [n/a]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:51)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:116)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:143)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.connection(StatementPreparerImpl.java:51)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$5.doPrepare(StatementPreparerImpl.java:150)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:177)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:152)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.lambda$list$0(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:227)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:200)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:109)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at jdk.internal.reflect.GeneratedMethodAccessor48.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$DeferredQueryInvocationHandler.invoke(SharedEntityManagerCreator.java:405)
	at jdk.proxy2/jdk.proxy2.$Proxy138.getResultList(Unknown Source)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$CollectionExecution.doExecute(JpaQueryExecution.java:129)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:149)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 22 more
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 396874ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:181)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:146)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:38)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:113)
	... 67 more
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2451)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:561)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionDead(PoolBase.java:168)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:170)
	... 72 more
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at jdk.internal.reflect.GeneratedConstructorAccessor46.newInstance(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:753)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:559)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2447)
	... 75 more
2025-07-18T09:15:10.851+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m41s867ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T09:15:36.071+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@294a88f9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.072+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6284def0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.072+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6c9943bf (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.072+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@11e91395 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.073+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@65fcbfe7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.073+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@214b9ef6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.074+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7e9b5db (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.074+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5e456b08 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.074+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5ec6e4a0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:15:36.075+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@23876da0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T09:25:05.710+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=54s789ms).
2025-07-18T09:25:15.314+08:00  WARN 90898 --- [nio-8080-exec-5] com.zaxxer.hikari.pool.ProxyConnection   : HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@bb0d91c marked as broken because of SQLSTATE(08S01), ErrorCode(0)

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 21 milliseconds ago. The last packet sent successfully to the server was 21 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.setReadOnlyInternal(ConnectionImpl.java:2121) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.setReadOnly(ConnectionImpl.java:2105) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.zaxxer.hikari.pool.ProxyConnection.setReadOnly(ProxyConnection.java:410) ~[HikariCP-5.0.1.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.setReadOnly(HikariProxyConnection.java) ~[HikariCP-5.0.1.jar:na]
	at org.springframework.jdbc.datasource.DataSourceUtils.prepareConnectionForTransaction(DataSourceUtils.java:190) ~[spring-jdbc-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.beginTransaction(HibernateJpaDialect.java:165) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:420) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:531) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:610) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.1.1.jar:6.1.1]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249) ~[spring-aop-6.1.1.jar:6.1.1]
	at jdk.proxy2/jdk.proxy2.$Proxy120.findById(Unknown Source) ~[na:na]
	at com.nseedu.courseeval.service.TaskService.getTaskStatus(TaskService.java:88) ~[classes/:na]
	at com.nseedu.courseeval.controller.TaskController.getTaskStatus(TaskController.java:47) ~[classes/:na]
	at jdk.internal.reflect.GeneratedMethodAccessor51.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.1.1.jar:6.1.1]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.16.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.1.1.jar:6.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.1.jar:6.1.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 21 milliseconds ago. The last packet sent successfully to the server was 21 milliseconds ago.
	at jdk.internal.reflect.GeneratedConstructorAccessor45.newInstance(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1052) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:998) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:655) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.setReadOnlyInternal(ConnectionImpl.java:2114) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 71 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576) ~[mysql-connector-j-8.1.0.jar:8.1.0]
	... 77 common frames omitted

❌ 查询任务状态失败: Could not open JPA EntityManager for transaction
2025-07-18T09:25:15.365+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1b7fbec8 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.366+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@c6e3b58 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.366+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1540e36 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.367+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@60a5f700 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.367+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@64a8c22 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.367+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7b03c348 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.368+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5a8747a0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.368+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@15d5a607 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:25:15.369+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@47ac1303 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T09:30:35.206+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@35b4a04b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.206+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4320b767 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.207+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4ab66ae8 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.207+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5a102d0d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.207+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5849a45c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.208+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1e94bbbd (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.208+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@678c73ad (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.208+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3c2f5214 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.209+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@29517a89 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:30:35.209+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@12a3654d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T09:44:14.249+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7bbb00e6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.250+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@28ab7226 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.251+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@54cb0052 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.252+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@77d4861d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.252+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3cb4526f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.253+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4ffd2d9d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.254+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@43387da0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.254+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@108fffb4 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:44:14.262+08:00  WARN 90898 --- [nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@e36f0e0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.id=?
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
📊 查询任务状态: taskId=17, status=FAILED
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        et1_0.id,
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        c1_0.teacher_id,
        c1_0.title,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        e1_0.id,
        e1_0.created_at,
        e1_0.password,
        e1_0.role,
        e1_0.username,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    join
        course c1_0 
            on c1_0.id=et1_0.course_id 
    join
        user e1_0 
            on e1_0.id=et1_0.evaluator_id 
    where
        et1_0.course_id=? 
    order by
        et1_0.created_at desc
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        cm1_0.id,
        cm1_0.checksum,
        cm1_0.course_id,
        cm1_0.created_at,
        cm1_0.duration_sec,
        cm1_0.file_path,
        cm1_0.file_url,
        cm1_0.media_type,
        cm1_0.size_bytes,
        cm1_0.storage_type 
    from
        course_media cm1_0 
    where
        cm1_0.course_id=? 
        and cm1_0.media_type=? 
    order by
        cm1_0.created_at
Hibernate: 
    select
        ts1_0.id,
        ts1_0.end_ms,
        ts1_0.media_id,
        ts1_0.start_ms,
        ts1_0.text 
    from
        transcript_sentence ts1_0 
    where
        ts1_0.media_id=? 
    order by
        ts1_0.start_ms
Hibernate: 
    select
        es1_0.id,
        es1_0.comment,
        d1_0.id,
        d1_0.description,
        d1_0.max_score,
        d1_0.name,
        d1_0.rubric_version_id,
        d1_0.weight,
        es1_0.dimension_id,
        es1_0.score,
        es1_0.task_id 
    from
        evaluation_score es1_0 
    join
        rubric_dimension d1_0 
            on d1_0.id=es1_0.dimension_id 
    where
        es1_0.task_id=? 
    order by
        es1_0.dimension_id
2025-07-18T09:45:06.015+08:00  WARN 90898 --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m87ms).
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T09:45:16.105+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4293a966 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:45:16.106+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2ea43886 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:45:16.106+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@65082bd5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T09:45:16.107+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3a0f768b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T10:15:25.552+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2f9783a0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:15:25.552+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@47f90ebe (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:15:25.553+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4c3c0db0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:15:25.553+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2fdfd070 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T10:44:33.501+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4839f6ba (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.502+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@371af228 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.503+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3b646b4c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.504+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3c6fc6bc (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.504+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7bf96401 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.505+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1b9252e4 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.506+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3b474d1 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.506+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@412c183d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T10:44:33.507+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7145a575 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
2025-07-18T11:13:51.717+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@728a3133 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.717+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@155d863a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.718+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@c8ea00c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.718+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3f515d8a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.718+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1c71e0fa (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.719+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6564c918 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.719+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1c139b78 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.719+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@299b287d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-18T11:13:51.720+08:00  WARN 90898 --- [   scheduling-1] com.zaxxer.hikari.pool.PoolBase          : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3afe6b84 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
Hibernate: 
    select
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        t1_0.id,
        t1_0.created_at,
        t1_0.password,
        t1_0.role,
        t1_0.username,
        c1_0.teacher_id,
        c1_0.title 
    from
        course c1_0 
    join
        user t1_0 
            on t1_0.id=c1_0.teacher_id 
    order by
        c1_0.id
Hibernate: 
    select
        c1_0.id,
        c1_0.created_at,
        c1_0.description,
        t1_0.id,
        t1_0.created_at,
        t1_0.password,
        t1_0.role,
        t1_0.username,
        c1_0.teacher_id,
        c1_0.title 
    from
        course c1_0 
    join
        user t1_0 
            on t1_0.id=c1_0.teacher_id 
    order by
        c1_0.id
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
Hibernate: 
    select
        et1_0.id,
        et1_0.course_id,
        et1_0.created_at,
        et1_0.error_msg,
        et1_0.evaluator_id,
        et1_0.finished_at,
        et1_0.progress,
        et1_0.rubric_version_id,
        et1_0.started_at,
        et1_0.status 
    from
        evaluation_task et1_0 
    where
        et1_0.status=? 
        and et1_0.created_at<?
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  16:48 h
[INFO] Finished at: 2025-07-18T11:29:13+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.0:run (default-cli) on project courseeval: Process terminated with exit code: 137 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
