# 媒体管理 API 文档

## 概述

媒体管理模块提供了完整的视频文件上传、存储、访问和管理功能，支持课程视频的上传、播放、下载和删除等操作。

## API 端点

### 1. 上传媒体文件

**POST** `/api/v1/media/upload`

上传视频文件到指定课程。

**请求参数：**
- `file` (multipart/form-data): 视频文件
- `courseId` (form-data): 课程ID

**支持的视频格式：**
- MP4, AVI, MOV, WMV, FLV, WebM, MKV

**响应示例：**
```json
{
  "mediaId": "123",
  "fileName": "lecture_video.mp4",
  "courseId": 1,
  "savedPath": "/uploads/20241218_120000_abc12345_lecture_video.mp4",
  "success": true
}
```

### 2. 获取课程媒体文件列表

**GET** `/api/v1/media/course/{courseId}`

获取指定课程的所有媒体文件。

**响应示例：**
```json
[
  {
    "id": 123,
    "mediaType": "VIDEO",
    "storageType": "LOCAL",
    "sizeBytes": 1024000,
    "durationSec": null,
    "createdAt": "2024-12-18T12:00:00",
    "originalFileName": "lecture_video.mp4"
  }
]
```

### 3. 获取单个媒体文件信息

**GET** `/api/v1/media/{mediaId}`

获取指定媒体文件的详细信息。

**响应示例：**
```json
{
  "id": 123,
  "courseId": 1,
  "mediaType": "VIDEO",
  "storageType": "LOCAL",
  "sizeBytes": 1024000,
  "durationSec": null,
  "createdAt": "2024-12-18T12:00:00",
  "originalFileName": "lecture_video.mp4"
}
```

### 4. 删除媒体文件

**DELETE** `/api/v1/media/{mediaId}`

删除指定的媒体文件（包括物理文件和数据库记录）。

**响应示例：**
```json
{
  "success": true,
  "message": "媒体文件删除成功"
}
```

### 5. 下载媒体文件

**GET** `/api/v1/media/{mediaId}/download`

下载媒体文件到本地。

**响应：** 文件流，Content-Disposition 为 attachment

### 6. 流式播放媒体文件

**GET** `/api/v1/media/{mediaId}/stream`

用于视频播放器的流式访问。

**响应：** 文件流，Content-Disposition 为 inline

### 7. 获取媒体文件播放URL

**GET** `/api/v1/media/{mediaId}/url`

获取媒体文件的播放和下载URL。

**响应示例：**
```json
{
  "mediaId": 123,
  "streamUrl": "/api/v1/media/123/stream",
  "downloadUrl": "/api/v1/media/123/download",
  "mediaInfo": {
    "id": 123,
    "courseId": 1,
    "mediaType": "VIDEO",
    "originalFileName": "lecture_video.mp4"
  }
}
```

### 8. 批量删除媒体文件

**DELETE** `/api/v1/media/batch`

批量删除多个媒体文件。

**请求体：**
```json
{
  "mediaIds": [123, 124, 125]
}
```

**响应示例：**
```json
{
  "successCount": 2,
  "failedCount": 1,
  "successIds": [123, 124],
  "failedItems": [
    {
      "mediaId": 125,
      "reason": "媒体文件不存在"
    }
  ]
}
```

### 9. 获取课程媒体统计信息

**GET** `/api/v1/media/course/{courseId}/stats`

获取指定课程的媒体文件统计信息。

**响应示例：**
```json
{
  "totalCount": 5,
  "typeStats": {
    "VIDEO": 3,
    "IMAGE": 2
  },
  "storageStats": {
    "LOCAL": 5
  },
  "totalSizeBytes": 5120000,
  "totalSizeMB": 4.88,
  "latestUpload": "2024-12-18T12:00:00"
}
```

### 10. 根据类型获取课程媒体文件

**GET** `/api/v1/media/course/{courseId}/type/{mediaType}`

获取指定课程的特定类型媒体文件。

**路径参数：**
- `courseId`: 课程ID
- `mediaType`: 媒体类型 (VIDEO, AUDIO, SLIDE, IMAGE)

**响应：** 与获取课程媒体文件列表相同的格式

## 错误处理

所有API都包含统一的错误处理：

**客户端错误 (4xx)：**
```json
{
  "error": "错误描述信息"
}
```

**服务器错误 (5xx)：**
```json
{
  "error": "服务器内部错误描述"
}
```

## 文件存储

- **存储位置：** 由配置项 `file.upload-dir` 指定
- **文件命名：** `yyyyMMdd_HHmmss_uuid_originalName` 格式
- **支持的存储类型：** LOCAL（本地存储）、DB（数据库存储，预留）

## 安全考虑

1. **文件类型验证：** 双重校验（Content-Type + 文件扩展名）
2. **文件大小限制：** 由Spring Boot配置控制
3. **路径安全：** 使用UUID防止路径遍历攻击
4. **CORS配置：** 支持指定的前端域名

## 使用示例

### 前端上传视频

```javascript
const formData = new FormData();
formData.append('file', videoFile);
formData.append('courseId', '1');

const response = await fetch('/api/v1/media/upload', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log('上传成功:', result.mediaId);
```

### 前端播放视频

```html
<video controls>
  <source src="/api/v1/media/123/stream" type="video/mp4">
</video>
```
