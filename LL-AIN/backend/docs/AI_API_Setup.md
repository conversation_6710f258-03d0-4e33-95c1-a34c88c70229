# AI API 配置指南

## 🎯 概述

本系统支持完整的真实AI评估流程：
1. **音频提取** - 使用FFmpeg从视频中提取音频
2. **语音转写** - 使用阿里云NLS进行语音识别
3. **AI评价** - 使用通义千问进行智能评估

## 🔧 配置步骤

### 1. 阿里云配置

#### 1.1 获取访问密钥
1. 登录 [阿里云控制台](https://ram.console.aliyun.com/manage/ak)
2. 创建AccessKey ID和AccessKey Secret
3. 记录下这两个值

#### 1.2 开通NLS语音识别服务
1. 访问 [智能语音交互控制台](https://nls-portal.console.aliyun.com/)
2. 开通"录音文件识别"服务
3. 创建项目，获取AppKey

#### 1.3 开通OSS对象存储（可选）
1. 访问 [OSS控制台](https://oss.console.aliyun.com/)
2. 创建Bucket
3. 记录Endpoint和Bucket名称

### 2. 通义千问配置

#### 2.1 获取API Key
1. 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 开通通义千问服务
3. 获取API Key

### 3. 环境变量配置

编辑 `backend/.env` 文件：

```bash
# 启用AI功能
AI_ENABLED=true

# 阿里云配置
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret

# NLS语音识别配置
ALIYUN_NLS_APP_KEY=your_nls_app_key
ALIYUN_NLS_REGION_ID=cn-shanghai
ALIYUN_NLS_DOMAIN=nls-meta.cn-shanghai.aliyuncs.com

# OSS存储配置（可选）
ALIYUN_OSS_ENDPOINT=your_oss_endpoint
ALIYUN_OSS_BUCKET_NAME=your_bucket_name

# Qwen大模型配置
QWEN_MODEL_NAME=qwen-max
QWEN_API_URL=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
QWEN_API_KEY=your_qwen_api_key
```

## 🚀 启动和测试

### 1. 启动服务
```bash
cd backend
mvn spring-boot:run
```

### 2. 查看配置状态
启动后查看控制台输出，会显示：
```
=== API配置状态检查 ===
🤖 AI功能启用: ✅ 是
☁️ 阿里云配置: ✅ 已配置
🧠 Qwen配置: ✅ 已配置
🚀 可使用真实AI: ✅ 是
========================
```

### 3. 测试评估流程
1. 上传视频文件
2. 创建评估任务
3. 观察控制台日志，应该看到：
   - 🎵 音频提取完成
   - 🎤 阿里云语音转写完成
   - 🧠 AI评价完成

## 🎭 降级模式

如果API未配置或调用失败，系统会自动降级到智能模拟模式：
- 仍然会提取真实音频
- 使用智能算法生成模拟转录
- 基于内容分析进行智能评分

## 💰 费用说明

- **阿里云NLS**: 按转写时长计费，约0.006元/分钟
- **通义千问**: 按Token计费，约0.002元/1000Token
- **OSS存储**: 按存储量计费，约0.12元/GB/月

## 🔍 故障排除

### 常见问题

1. **语音转写失败**
   - 检查音频文件格式（支持WAV、MP3等）
   - 确认NLS AppKey正确
   - 检查网络连接

2. **AI评价失败**
   - 检查Qwen API Key是否有效
   - 确认账户余额充足
   - 检查API调用频率限制

3. **音频提取失败**
   - 确认FFmpeg已安装
   - 检查视频文件路径和权限
   - 查看FFmpeg错误日志

### 日志分析
关注控制台中的关键日志：
- `🎵 音频提取完成` - 音频提取成功
- `🎤 阿里云语音转写完成` - 转写成功
- `🧠 AI评价完成` - 评价成功
- `❌` 开头的日志 - 错误信息

## 📞 技术支持

如遇问题，请检查：
1. 环境变量配置是否正确
2. API密钥是否有效
3. 服务是否已开通
4. 网络连接是否正常
