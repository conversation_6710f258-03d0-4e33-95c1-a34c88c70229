"use strict";var wD=Object.create;var d=Object.defineProperty;var MD=Object.getOwnPropertyDescriptor;var OD=Object.getOwnPropertyNames;var SD=Object.getPrototypeOf,RD=Object.prototype.hasOwnProperty;var E=(D,u)=>()=>(u||D((u={exports:{}}).exports,u),u.exports),ND=(D,u)=>{for(var t in u)d(D,t,{get:u[t],enumerable:!0})},G=(D,u,t,e)=>{if(u&&typeof u=="object"||typeof u=="function")for(let r of OD(u))!RD.call(D,r)&&r!==t&&d(D,r,{get:()=>u[r],enumerable:!(e=MD(u,r))||e.enumerable});return D};var M=(D,u,t)=>(t=D!=null?wD(SD(D)):{},G(u||!D||!D.__esModule?d(t,"default",{value:D,enumerable:!0}):t,D)),jD=D=>G(d({},"__esModule",{value:!0}),D);var Z=E((Ou,Y)=>{"use strict";Y.exports=({onlyFirst:D=!1}={})=>{let u=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(u,D?void 0:"g")}});var J=E((Su,H)=>{"use strict";var PD=Z();H.exports=D=>typeof D=="string"?D.replace(PD(),""):D});var R=E((Ru,S)=>{"use strict";var K=D=>Number.isNaN(D)?!1:D>=4352&&(D<=4447||D===9001||D===9002||11904<=D&&D<=12871&&D!==12351||12880<=D&&D<=19903||19968<=D&&D<=42182||43360<=D&&D<=43388||44032<=D&&D<=55203||63744<=D&&D<=64255||65040<=D&&D<=65049||65072<=D&&D<=65131||65281<=D&&D<=65376||65504<=D&&D<=65510||110592<=D&&D<=110593||127488<=D&&D<=127569||131072<=D&&D<=262141);S.exports=K;S.exports.default=K});var DD=E((Nu,X)=>{"use strict";var Q="[\uD800-\uDBFF][\uDC00-\uDFFF]",ID=D=>D&&D.exact?new RegExp(`^${Q}$`):new RegExp(Q,"g");X.exports=ID});var eD=E((ju,uD)=>{"use strict";uD.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}});var N=E((Tu,rD)=>{"use strict";var x=eD(),tD={};for(let D of Object.keys(x))tD[x[D]]=D;var i={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};rD.exports=i;for(let D of Object.keys(i)){if(!("channels"in i[D]))throw new Error("missing channels property: "+D);if(!("labels"in i[D]))throw new Error("missing channel labels property: "+D);if(i[D].labels.length!==i[D].channels)throw new Error("channel and label counts mismatch: "+D);let{channels:u,labels:t}=i[D];delete i[D].channels,delete i[D].labels,Object.defineProperty(i[D],"channels",{value:u}),Object.defineProperty(i[D],"labels",{value:t})}i.rgb.hsl=function(D){let u=D[0]/255,t=D[1]/255,e=D[2]/255,r=Math.min(u,t,e),n=Math.max(u,t,e),o=n-r,s,a;n===r?s=0:u===n?s=(t-e)/o:t===n?s=2+(e-u)/o:e===n&&(s=4+(u-t)/o),s=Math.min(s*60,360),s<0&&(s+=360);let F=(r+n)/2;return n===r?a=0:F<=.5?a=o/(n+r):a=o/(2-n-r),[s,a*100,F*100]};i.rgb.hsv=function(D){let u,t,e,r,n,o=D[0]/255,s=D[1]/255,a=D[2]/255,F=Math.max(o,s,a),l=F-Math.min(o,s,a),C=function(b){return(F-b)/6/l+1/2};return l===0?(r=0,n=0):(n=l/F,u=C(o),t=C(s),e=C(a),o===F?r=e-t:s===F?r=1/3+u-e:a===F&&(r=2/3+t-u),r<0?r+=1:r>1&&(r-=1)),[r*360,n*100,F*100]};i.rgb.hwb=function(D){let u=D[0],t=D[1],e=D[2],r=i.rgb.hsl(D)[0],n=1/255*Math.min(u,Math.min(t,e));return e=1-1/255*Math.max(u,Math.max(t,e)),[r,n*100,e*100]};i.rgb.cmyk=function(D){let u=D[0]/255,t=D[1]/255,e=D[2]/255,r=Math.min(1-u,1-t,1-e),n=(1-u-r)/(1-r)||0,o=(1-t-r)/(1-r)||0,s=(1-e-r)/(1-r)||0;return[n*100,o*100,s*100,r*100]};function qD(D,u){return(D[0]-u[0])**2+(D[1]-u[1])**2+(D[2]-u[2])**2}i.rgb.keyword=function(D){let u=tD[D];if(u)return u;let t=1/0,e;for(let r of Object.keys(x)){let n=x[r],o=qD(D,n);o<t&&(t=o,e=r)}return e};i.keyword.rgb=function(D){return x[D]};i.rgb.xyz=function(D){let u=D[0]/255,t=D[1]/255,e=D[2]/255;u=u>.04045?((u+.055)/1.055)**2.4:u/12.92,t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,e=e>.04045?((e+.055)/1.055)**2.4:e/12.92;let r=u*.4124+t*.3576+e*.1805,n=u*.2126+t*.7152+e*.0722,o=u*.0193+t*.1192+e*.9505;return[r*100,n*100,o*100]};i.rgb.lab=function(D){let u=i.rgb.xyz(D),t=u[0],e=u[1],r=u[2];t/=95.047,e/=100,r/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,e=e>.008856?e**(1/3):7.787*e+16/116,r=r>.008856?r**(1/3):7.787*r+16/116;let n=116*e-16,o=500*(t-e),s=200*(e-r);return[n,o,s]};i.hsl.rgb=function(D){let u=D[0]/360,t=D[1]/100,e=D[2]/100,r,n,o;if(t===0)return o=e*255,[o,o,o];e<.5?r=e*(1+t):r=e+t-e*t;let s=2*e-r,a=[0,0,0];for(let F=0;F<3;F++)n=u+1/3*-(F-1),n<0&&n++,n>1&&n--,6*n<1?o=s+(r-s)*6*n:2*n<1?o=r:3*n<2?o=s+(r-s)*(2/3-n)*6:o=s,a[F]=o*255;return a};i.hsl.hsv=function(D){let u=D[0],t=D[1]/100,e=D[2]/100,r=t,n=Math.max(e,.01);e*=2,t*=e<=1?e:2-e,r*=n<=1?n:2-n;let o=(e+t)/2,s=e===0?2*r/(n+r):2*t/(e+t);return[u,s*100,o*100]};i.hsv.rgb=function(D){let u=D[0]/60,t=D[1]/100,e=D[2]/100,r=Math.floor(u)%6,n=u-Math.floor(u),o=255*e*(1-t),s=255*e*(1-t*n),a=255*e*(1-t*(1-n));switch(e*=255,r){case 0:return[e,a,o];case 1:return[s,e,o];case 2:return[o,e,a];case 3:return[o,s,e];case 4:return[a,o,e];case 5:return[e,o,s]}};i.hsv.hsl=function(D){let u=D[0],t=D[1]/100,e=D[2]/100,r=Math.max(e,.01),n,o;o=(2-t)*e;let s=(2-t)*r;return n=t*r,n/=s<=1?s:2-s,n=n||0,o/=2,[u,n*100,o*100]};i.hwb.rgb=function(D){let u=D[0]/360,t=D[1]/100,e=D[2]/100,r=t+e,n;r>1&&(t/=r,e/=r);let o=Math.floor(6*u),s=1-e;n=6*u-o,o&1&&(n=1-n);let a=t+n*(s-t),F,l,C;switch(o){default:case 6:case 0:F=s,l=a,C=t;break;case 1:F=a,l=s,C=t;break;case 2:F=t,l=s,C=a;break;case 3:F=t,l=a,C=s;break;case 4:F=a,l=t,C=s;break;case 5:F=s,l=t,C=a;break}return[F*255,l*255,C*255]};i.cmyk.rgb=function(D){let u=D[0]/100,t=D[1]/100,e=D[2]/100,r=D[3]/100,n=1-Math.min(1,u*(1-r)+r),o=1-Math.min(1,t*(1-r)+r),s=1-Math.min(1,e*(1-r)+r);return[n*255,o*255,s*255]};i.xyz.rgb=function(D){let u=D[0]/100,t=D[1]/100,e=D[2]/100,r,n,o;return r=u*3.2406+t*-1.5372+e*-.4986,n=u*-.9689+t*1.8758+e*.0415,o=u*.0557+t*-.204+e*1.057,r=r>.0031308?1.055*r**(1/2.4)-.055:r*12.92,n=n>.0031308?1.055*n**(1/2.4)-.055:n*12.92,o=o>.0031308?1.055*o**(1/2.4)-.055:o*12.92,r=Math.min(Math.max(0,r),1),n=Math.min(Math.max(0,n),1),o=Math.min(Math.max(0,o),1),[r*255,n*255,o*255]};i.xyz.lab=function(D){let u=D[0],t=D[1],e=D[2];u/=95.047,t/=100,e/=108.883,u=u>.008856?u**(1/3):7.787*u+16/116,t=t>.008856?t**(1/3):7.787*t+16/116,e=e>.008856?e**(1/3):7.787*e+16/116;let r=116*t-16,n=500*(u-t),o=200*(t-e);return[r,n,o]};i.lab.xyz=function(D){let u=D[0],t=D[1],e=D[2],r,n,o;n=(u+16)/116,r=t/500+n,o=n-e/200;let s=n**3,a=r**3,F=o**3;return n=s>.008856?s:(n-16/116)/7.787,r=a>.008856?a:(r-16/116)/7.787,o=F>.008856?F:(o-16/116)/7.787,r*=95.047,n*=100,o*=108.883,[r,n,o]};i.lab.lch=function(D){let u=D[0],t=D[1],e=D[2],r;r=Math.atan2(e,t)*360/2/Math.PI,r<0&&(r+=360);let o=Math.sqrt(t*t+e*e);return[u,o,r]};i.lch.lab=function(D){let u=D[0],t=D[1],r=D[2]/360*2*Math.PI,n=t*Math.cos(r),o=t*Math.sin(r);return[u,n,o]};i.rgb.ansi16=function(D,u=null){let[t,e,r]=D,n=u===null?i.rgb.hsv(D)[2]:u;if(n=Math.round(n/50),n===0)return 30;let o=30+(Math.round(r/255)<<2|Math.round(e/255)<<1|Math.round(t/255));return n===2&&(o+=60),o};i.hsv.ansi16=function(D){return i.rgb.ansi16(i.hsv.rgb(D),D[2])};i.rgb.ansi256=function(D){let u=D[0],t=D[1],e=D[2];return u===t&&t===e?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(t/255*5)+Math.round(e/255*5)};i.ansi16.rgb=function(D){let u=D%10;if(u===0||u===7)return D>50&&(u+=3.5),u=u/10.5*255,[u,u,u];let t=(~~(D>50)+1)*.5,e=(u&1)*t*255,r=(u>>1&1)*t*255,n=(u>>2&1)*t*255;return[e,r,n]};i.ansi256.rgb=function(D){if(D>=232){let n=(D-232)*10+8;return[n,n,n]}D-=16;let u,t=Math.floor(D/36)/5*255,e=Math.floor((u=D%36)/6)/5*255,r=u%6/5*255;return[t,e,r]};i.rgb.hex=function(D){let t=(((Math.round(D[0])&255)<<16)+((Math.round(D[1])&255)<<8)+(Math.round(D[2])&255)).toString(16).toUpperCase();return"000000".substring(t.length)+t};i.hex.rgb=function(D){let u=D.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!u)return[0,0,0];let t=u[0];u[0].length===3&&(t=t.split("").map(s=>s+s).join(""));let e=parseInt(t,16),r=e>>16&255,n=e>>8&255,o=e&255;return[r,n,o]};i.rgb.hcg=function(D){let u=D[0]/255,t=D[1]/255,e=D[2]/255,r=Math.max(Math.max(u,t),e),n=Math.min(Math.min(u,t),e),o=r-n,s,a;return o<1?s=n/(1-o):s=0,o<=0?a=0:r===u?a=(t-e)/o%6:r===t?a=2+(e-u)/o:a=4+(u-t)/o,a/=6,a%=1,[a*360,o*100,s*100]};i.hsl.hcg=function(D){let u=D[1]/100,t=D[2]/100,e=t<.5?2*u*t:2*u*(1-t),r=0;return e<1&&(r=(t-.5*e)/(1-e)),[D[0],e*100,r*100]};i.hsv.hcg=function(D){let u=D[1]/100,t=D[2]/100,e=u*t,r=0;return e<1&&(r=(t-e)/(1-e)),[D[0],e*100,r*100]};i.hcg.rgb=function(D){let u=D[0]/360,t=D[1]/100,e=D[2]/100;if(t===0)return[e*255,e*255,e*255];let r=[0,0,0],n=u%1*6,o=n%1,s=1-o,a=0;switch(Math.floor(n)){case 0:r[0]=1,r[1]=o,r[2]=0;break;case 1:r[0]=s,r[1]=1,r[2]=0;break;case 2:r[0]=0,r[1]=1,r[2]=o;break;case 3:r[0]=0,r[1]=s,r[2]=1;break;case 4:r[0]=o,r[1]=0,r[2]=1;break;default:r[0]=1,r[1]=0,r[2]=s}return a=(1-t)*e,[(t*r[0]+a)*255,(t*r[1]+a)*255,(t*r[2]+a)*255]};i.hcg.hsv=function(D){let u=D[1]/100,t=D[2]/100,e=u+t*(1-u),r=0;return e>0&&(r=u/e),[D[0],r*100,e*100]};i.hcg.hsl=function(D){let u=D[1]/100,e=D[2]/100*(1-u)+.5*u,r=0;return e>0&&e<.5?r=u/(2*e):e>=.5&&e<1&&(r=u/(2*(1-e))),[D[0],r*100,e*100]};i.hcg.hwb=function(D){let u=D[1]/100,t=D[2]/100,e=u+t*(1-u);return[D[0],(e-u)*100,(1-e)*100]};i.hwb.hcg=function(D){let u=D[1]/100,e=1-D[2]/100,r=e-u,n=0;return r<1&&(n=(e-r)/(1-r)),[D[0],r*100,n*100]};i.apple.rgb=function(D){return[D[0]/65535*255,D[1]/65535*255,D[2]/65535*255]};i.rgb.apple=function(D){return[D[0]/255*65535,D[1]/255*65535,D[2]/255*65535]};i.gray.rgb=function(D){return[D[0]/100*255,D[0]/100*255,D[0]/100*255]};i.gray.hsl=function(D){return[0,0,D[0]]};i.gray.hsv=i.gray.hsl;i.gray.hwb=function(D){return[0,100,D[0]]};i.gray.cmyk=function(D){return[0,0,0,D[0]]};i.gray.lab=function(D){return[D[0],0,0]};i.gray.hex=function(D){let u=Math.round(D[0]/100*255)&255,e=((u<<16)+(u<<8)+u).toString(16).toUpperCase();return"000000".substring(e.length)+e};i.rgb.gray=function(D){return[(D[0]+D[1]+D[2])/3/255*100]}});var oD=E((Pu,nD)=>{"use strict";var A=N();function LD(){let D={},u=Object.keys(A);for(let t=u.length,e=0;e<t;e++)D[u[e]]={distance:-1,parent:null};return D}function $D(D){let u=LD(),t=[D];for(u[D].distance=0;t.length;){let e=t.pop(),r=Object.keys(A[e]);for(let n=r.length,o=0;o<n;o++){let s=r[o],a=u[s];a.distance===-1&&(a.distance=u[e].distance+1,a.parent=e,t.unshift(s))}}return u}function GD(D,u){return function(t){return u(D(t))}}function zD(D,u){let t=[u[D].parent,D],e=A[u[D].parent][D],r=u[D].parent;for(;u[r].parent;)t.unshift(u[r].parent),e=GD(A[u[r].parent][r],e),r=u[r].parent;return e.conversion=t,e}nD.exports=function(D){let u=$D(D),t={},e=Object.keys(u);for(let r=e.length,n=0;n<r;n++){let o=e[n];u[o].parent!==null&&(t[o]=zD(o,u))}return t}});var aD=E((Iu,sD)=>{"use strict";var j=N(),_D=oD(),h={},WD=Object.keys(j);function VD(D){let u=function(...t){let e=t[0];return e==null?e:(e.length>1&&(t=e),D(t))};return"conversion"in D&&(u.conversion=D.conversion),u}function UD(D){let u=function(...t){let e=t[0];if(e==null)return e;e.length>1&&(t=e);let r=D(t);if(typeof r=="object")for(let n=r.length,o=0;o<n;o++)r[o]=Math.round(r[o]);return r};return"conversion"in D&&(u.conversion=D.conversion),u}WD.forEach(D=>{h[D]={},Object.defineProperty(h[D],"channels",{value:j[D].channels}),Object.defineProperty(h[D],"labels",{value:j[D].labels});let u=_D(D);Object.keys(u).forEach(e=>{let r=u[e];h[D][e]=UD(r),h[D][e].raw=VD(r)})});sD.exports=h});var ED=E((qu,CD)=>{"use strict";var iD=(D,u)=>(...t)=>`\x1B[${D(...t)+u}m`,FD=(D,u)=>(...t)=>{let e=D(...t);return`\x1B[${38+u};5;${e}m`},cD=(D,u)=>(...t)=>{let e=D(...t);return`\x1B[${38+u};2;${e[0]};${e[1]};${e[2]}m`},k=D=>D,lD=(D,u,t)=>[D,u,t],m=(D,u,t)=>{Object.defineProperty(D,u,{get:()=>{let e=t();return Object.defineProperty(D,u,{value:e,enumerable:!0,configurable:!0}),e},enumerable:!0,configurable:!0})},T,B=(D,u,t,e)=>{T===void 0&&(T=aD());let r=e?10:0,n={};for(let[o,s]of Object.entries(T)){let a=o==="ansi16"?"ansi":o;o===u?n[a]=D(t,r):typeof s=="object"&&(n[a]=D(s[u],r))}return n};function YD(){let D=new Map,u={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};u.color.gray=u.color.blackBright,u.bgColor.bgGray=u.bgColor.bgBlackBright,u.color.grey=u.color.blackBright,u.bgColor.bgGrey=u.bgColor.bgBlackBright;for(let[t,e]of Object.entries(u)){for(let[r,n]of Object.entries(e))u[r]={open:`\x1B[${n[0]}m`,close:`\x1B[${n[1]}m`},e[r]=u[r],D.set(n[0],n[1]);Object.defineProperty(u,t,{value:e,enumerable:!1})}return Object.defineProperty(u,"codes",{value:D,enumerable:!1}),u.color.close="\x1B[39m",u.bgColor.close="\x1B[49m",m(u.color,"ansi",()=>B(iD,"ansi16",k,!1)),m(u.color,"ansi256",()=>B(FD,"ansi256",k,!1)),m(u.color,"ansi16m",()=>B(cD,"rgb",lD,!1)),m(u.bgColor,"ansi",()=>B(iD,"ansi16",k,!0)),m(u.bgColor,"ansi256",()=>B(FD,"ansi256",k,!0)),m(u.bgColor,"ansi16m",()=>B(cD,"rgb",lD,!0)),u}Object.defineProperty(CD,"exports",{enumerable:!0,get:YD})});var mD=E((Lu,hD)=>{"use strict";var ZD=R(),HD=DD(),fD=ED(),gD=["\x1B","\x9B"],v=D=>`${gD[0]}[${D}m`,pD=(D,u,t)=>{let e=[];D=[...D];for(let r of D){let n=r;r.match(";")&&(r=r.split(";")[0][0]+"0");let o=fD.codes.get(parseInt(r,10));if(o){let s=D.indexOf(o.toString());s>=0?D.splice(s,1):e.push(v(u?o:n))}else if(u){e.push(v(0));break}else e.push(v(n))}if(u&&(e=e.filter((r,n)=>e.indexOf(r)===n),t!==void 0)){let r=v(fD.codes.get(parseInt(t,10)));e=e.reduce((n,o)=>o===r?[o,...n]:[...n,o],[])}return e.join("")};hD.exports=(D,u,t)=>{let e=[...D.normalize()],r=[];t=typeof t=="number"?t:e.length;let n=!1,o,s=0,a="";for(let[F,l]of e.entries()){let C=!1;if(gD.includes(l)){let b=/\d[^m]*/.exec(D.slice(F,F+18));o=b&&b.length>0?b[0]:void 0,s<t&&(n=!0,o!==void 0&&r.push(o))}else n&&l==="m"&&(n=!1,C=!0);if(!n&&!C&&++s,!HD({exact:!0}).test(l)&&ZD(l.codePointAt())&&++s,s>u&&s<=t)a+=l;else if(s===u&&!n&&o!==void 0)a=pD(r);else if(s>=t){a+=pD(r,!0,o);break}}return a}});var bD=E(($u,BD)=>{"use strict";BD.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});var I=E((Gu,P)=>{"use strict";var JD=J(),KD=R(),QD=bD(),xD=D=>{if(typeof D!="string"||D.length===0||(D=JD(D),D.length===0))return 0;D=D.replace(QD(),"  ");let u=0;for(let t=0;t<D.length;t++){let e=D.codePointAt(t);e<=31||e>=127&&e<=159||e>=768&&e<=879||(e>65535&&t++,u+=KD(e)?2:1)}return u};P.exports=xD;P.exports.default=xD});var yD=E((zu,dD)=>{"use strict";var g=mD(),XD=I();function w(D,u,t){if(D.charAt(u)===" ")return u;for(let e=1;e<=3;e++)if(t){if(D.charAt(u+e)===" ")return u+e}else if(D.charAt(u-e)===" ")return u-e;return u}dD.exports=(D,u,t)=>{t={position:"end",preferTruncationOnSpace:!1,...t};let{position:e,space:r,preferTruncationOnSpace:n}=t,o="\u2026",s=1;if(typeof D!="string")throw new TypeError(`Expected \`input\` to be a string, got ${typeof D}`);if(typeof u!="number")throw new TypeError(`Expected \`columns\` to be a number, got ${typeof u}`);if(u<1)return"";if(u===1)return o;let a=XD(D);if(a<=u)return D;if(e==="start"){if(n){let F=w(D,a-u+1,!0);return o+g(D,F,a).trim()}return r===!0&&(o+=" ",s=2),o+g(D,a-u+s,a)}if(e==="middle"){r===!0&&(o=" "+o+" ",s=3);let F=Math.floor(u/2);if(n){let l=w(D,F),C=w(D,a-(u-F)+1,!0);return g(D,0,l)+o+g(D,C,a).trim()}return g(D,0,F)+o+g(D,a-(u-F)+s,a)}if(e==="end"){if(n){let F=w(D,u-1);return g(D,0,F)+o}return r===!0&&(o=" "+o,s=2),g(D,0,u-s)+o}throw new Error(`Expected \`options.position\` to be either \`start\`, \`middle\` or \`end\`, got ${e}`)}});var eu={};ND(eu,{main:()=>uu,printMessageAndExitIfUnsupportedNodeVersion:()=>vD});module.exports=jD(eu);var $=M(require("process"));var O,z,_,W,V=!0;typeof process<"u"&&({FORCE_COLOR:O,NODE_DISABLE_COLORS:z,NO_COLOR:_,TERM:W}=process.env||{},V=process.stdout&&process.stdout.isTTY);var TD={enabled:!z&&_==null&&W!=="dumb"&&(O!=null&&O!=="0"||V)};function c(D,u){let t=new RegExp(`\\x1b\\[${u}m`,"g"),e=`\x1B[${D}m`,r=`\x1B[${u}m`;return function(n){return!TD.enabled||n==null?n:e+(~(""+n).indexOf(r)?n.replace(t,r+e):n)+r}}var y=c(0,0),U=c(1,22),ru=c(2,22),nu=c(3,23),ou=c(4,24),su=c(7,27),au=c(8,28),iu=c(9,29),Fu=c(30,39),cu=c(31,39),lu=c(32,39),Cu=c(33,39),Eu=c(34,39),fu=c(35,39),pu=c(36,39),gu=c(37,39),hu=c(90,39),p=c(90,39),mu=c(40,49),Bu=c(41,49),bu=c(42,49),xu=c(43,49),du=c(44,49),yu=c(45,49),Au=c(46,49),ku=c(47,49);var AD=M(yD());var q=M(I()),f={topLeft:"\u250C",topRight:"\u2510",bottomRight:"\u2518",bottomLeft:"\u2514",vertical:"\u2502",horizontal:"\u2500"};function Du(D){return D.split(`
`).reduce((u,t)=>Math.max(u,(0,q.default)(t)),0)+2}function L({title:D,width:u,height:t,str:e,horizontalPadding:r}){r=r||0,u=u||0,t=t||0,u=Math.max(u,Du(e)+r*2);let n=D?p(f.topLeft+f.horizontal)+" "+y(U(D))+" "+p(f.horizontal.repeat(u-D.length-2-3)+f.topRight)+y():p(f.topLeft+f.horizontal)+p(f.horizontal.repeat(u-3)+f.topRight),o=f.bottomLeft+f.horizontal.repeat(u-2)+f.bottomRight,s=e.split(`
`);s.length<t&&s.push(...new Array(t-s.length).fill(""));let a=s.slice(-t).map(F=>{let l=Math.min((0,q.default)(F),u),C=Math.max(u-l-2,0);return`${p(f.vertical)}${" ".repeat(r)}${y((0,AD.default)(F,u-2))}${" ".repeat(C-r)}${p(f.vertical)}`}).join(`
`);return p(n+`
`+a+`
`+o)}function uu(){vD($.default.version)}function kD(D){return D.split(".").slice(0,2).map(u=>parseInt(u,10))}function vD(D){let u=D.slice(1),[t,e]=kD(u),r="16.13",[n,o]=kD(r);(t<n||t===n&&e<o)&&(console.error(L({str:`Prisma only supports Node.js >= ${r}.
Please upgrade your Node.js version.`,height:2,width:48,horizontalPadding:4})),$.default.exit(1))}0&&(module.exports={main,printMessageAndExitIfUnsupportedNodeVersion});
