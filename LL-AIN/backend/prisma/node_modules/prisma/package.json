{"version": "5.22.0", "name": "prisma", "description": "Prisma is an open-source database toolkit. It includes a JavaScript/TypeScript ORM for Node.js, migrations and a modern GUI to view and edit the data in your database. You can use Prisma in new projects or add it to an existing one.", "keywords": ["CLI", "ORM", "Prisma", "Prisma CLI", "prisma2", "database", "db", "JavaScript", "JS", "TypeScript", "TS", "SQL", "SQLite", "pg", "Postgres", "PostgreSQL", "CockroachDB", "MySQL", "MariaDB", "MSSQL", "SQL Server", "SQLServer", "MongoDB"], "main": "build/index.js", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/cli"}, "homepage": "https://www.prisma.io", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "bugs": "https://github.com/prisma/prisma/issues", "license": "Apache-2.0", "engines": {"node": ">=16.13"}, "prisma": {"prismaCommit": "718358aa37975c18e5ea62f5b659fb47630b7609"}, "files": ["README.md", "build", "install", "runtime/*.js", "runtime/*.d.ts", "runtime/utils", "runtime/dist", "runtime/llhttp", "prisma-client", "preinstall", "scripts/preinstall-entry.js"], "pkg": {"assets": ["build/**/*", "runtime/**/*", "prisma-client/**/*", "node_modules/@prisma/engines/**/*", "node_modules/@prisma/engines/*"]}, "bin": {"prisma": "build/index.js"}, "devDependencies": {"@prisma/mini-proxy": "0.9.5", "@prisma/studio": "0.503.0", "@prisma/studio-server": "0.503.0", "@swc/core": "1.6.13", "@swc/jest": "0.2.36", "@types/debug": "4.1.12", "@types/fs-extra": "9.0.13", "@types/jest": "29.5.12", "@types/node": "18.19.31", "@types/rimraf": "3.0.2", "async-listen": "3.0.1", "checkpoint-client": "1.1.33", "chokidar": "3.6.0", "debug": "4.3.6", "dotenv": "16.0.3", "esbuild": "0.23.0", "execa": "5.1.1", "fast-glob": "3.3.2", "fs-extra": "11.1.1", "fs-jetpack": "5.1.0", "get-port": "5.1.1", "global-dirs": "4.0.0", "jest": "29.7.0", "jest-junit": "16.0.0", "kleur": "4.1.5", "line-replace": "2.0.1", "log-update": "4.0.0", "node-fetch": "3.3.2", "npm-packlist": "5.1.3", "open": "7.4.2", "pkg-up": "3.1.0", "resolve-pkg": "2.0.0", "rimraf": "3.0.2", "strip-ansi": "6.0.1", "ts-pattern": "5.2.0", "typescript": "5.4.5", "xdg-app-paths": "8.3.0", "zx": "7.2.3", "@prisma/fetch-engine": "5.22.0", "@prisma/client": "5.22.0", "@prisma/generator-helper": "5.22.0", "@prisma/get-platform": "5.22.0", "@prisma/internals": "5.22.0", "@prisma/debug": "5.22.0", "@prisma/migrate": "5.22.0"}, "dependencies": {"@prisma/engines": "5.22.0"}, "optionalDependencies": {"fsevents": "2.3.3"}, "sideEffects": false, "scripts": {"prisma": "tsx src/bin.ts", "platform": "tsx src/bin.ts platform --early-access", "pm": "tsx src/bin.ts platform --early-access", "dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "dotenv -e ../../.db.env -- tsx helpers/run-tests.ts", "test:platform": "dotenv -e ../../.db.env -- tsx helpers/run-tests.ts src/platform", "tsc": "tsc -d -p tsconfig.build.json", "preinstall": "node scripts/preinstall-entry.js"}}