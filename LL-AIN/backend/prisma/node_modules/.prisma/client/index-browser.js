
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  password: 'password',
  role: 'role',
  created_at: 'created_at'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  teacher_id: 'teacher_id',
  created_at: 'created_at'
};

exports.Prisma.CourseMediaScalarFieldEnum = {
  id: 'id',
  course_id: 'course_id',
  media_type: 'media_type',
  storage_type: 'storage_type',
  file_path: 'file_path',
  file_url: 'file_url',
  file_blob: 'file_blob',
  size_bytes: 'size_bytes',
  duration_sec: 'duration_sec',
  checksum: 'checksum',
  created_at: 'created_at'
};

exports.Prisma.TranscriptSentenceScalarFieldEnum = {
  id: 'id',
  media_id: 'media_id',
  start_ms: 'start_ms',
  end_ms: 'end_ms',
  text: 'text'
};

exports.Prisma.AnnotationScalarFieldEnum = {
  id: 'id',
  sentence_id: 'sentence_id',
  author_id: 'author_id',
  type: 'type',
  payload: 'payload',
  created_at: 'created_at'
};

exports.Prisma.RubricScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  created_at: 'created_at'
};

exports.Prisma.RubricVersionScalarFieldEnum = {
  id: 'id',
  rubric_id: 'rubric_id',
  version: 'version',
  schema: 'schema',
  description: 'description',
  is_active: 'is_active',
  created_at: 'created_at'
};

exports.Prisma.RubricDimensionScalarFieldEnum = {
  id: 'id',
  rubric_version_id: 'rubric_version_id',
  name: 'name',
  weight: 'weight',
  max_score: 'max_score',
  description: 'description'
};

exports.Prisma.EvaluationTaskScalarFieldEnum = {
  id: 'id',
  course_id: 'course_id',
  rubric_version_id: 'rubric_version_id',
  evaluator_id: 'evaluator_id',
  status: 'status',
  created_at: 'created_at',
  finished_at: 'finished_at',
  progress: 'progress',
  error_msg: 'error_msg',
  started_at: 'started_at'
};

exports.Prisma.EvaluationScoreScalarFieldEnum = {
  id: 'id',
  task_id: 'task_id',
  dimension_id: 'dimension_id',
  score: 'score',
  comment: 'comment'
};

exports.Prisma.EvaluationReportScalarFieldEnum = {
  id: 'id',
  task_id: 'task_id',
  pdf_url: 'pdf_url',
  created_at: 'created_at'
};

exports.Prisma.RubricCourseScalarFieldEnum = {
  rubric_version_id: 'rubric_version_id',
  course_id: 'course_id',
  assigned_at: 'assigned_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  EXPERT: 'EXPERT',
  TEACHER: 'TEACHER'
};

exports.MediaType = exports.$Enums.MediaType = {
  VIDEO: 'VIDEO',
  AUDIO: 'AUDIO',
  SLIDE: 'SLIDE',
  IMAGE: 'IMAGE'
};

exports.StorageType = exports.$Enums.StorageType = {
  LOCAL: 'LOCAL',
  DB: 'DB'
};

exports.AnnotationType = exports.$Enums.AnnotationType = {
  HIGHLIGHT: 'HIGHLIGHT',
  NOTE: 'NOTE'
};

exports.EvaluationStatus = exports.$Enums.EvaluationStatus = {
  PENDING: 'PENDING',
  RUNNING: 'RUNNING',
  DONE: 'DONE',
  FAILED: 'FAILED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Course: 'Course',
  CourseMedia: 'CourseMedia',
  TranscriptSentence: 'TranscriptSentence',
  Annotation: 'Annotation',
  Rubric: 'Rubric',
  RubricVersion: 'RubricVersion',
  RubricDimension: 'RubricDimension',
  EvaluationTask: 'EvaluationTask',
  EvaluationScore: 'EvaluationScore',
  EvaluationReport: 'EvaluationReport',
  RubricCourse: 'RubricCourse'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
