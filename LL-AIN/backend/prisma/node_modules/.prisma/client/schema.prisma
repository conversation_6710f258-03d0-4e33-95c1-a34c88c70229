// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextIndex"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 1. 用户表
model User {
  id         BigInt   @id @default(autoincrement())
  username   String   @unique @db.VarChar(64)
  password   String   @db.VarChar(128)
  role       UserR<PERSON>
  created_at DateTime @default(now())

  // 反向关系
  courses          Course[]
  annotations      Annotation[]
  evaluation_tasks EvaluationTask[]

  @@index([role])
  @@map("user")
}

enum UserRole {
  ADMIN
  EXPERT
  TEACHER
}

// 2. 课程表
model Course {
  id          BigInt   @id @default(autoincrement())
  title       String   @db.VarChar(255)
  description String?  @db.Text
  teacher_id  BigInt
  created_at  DateTime @default(now())

  // 关系
  teacher User @relation(fields: [teacher_id], references: [id])

  // 反向关系
  course_media     CourseMedia[]
  evaluation_tasks EvaluationTask[]
  rubric_courses   RubricCourse[]

  @@index([teacher_id])
  @@map("course")
}

// 3. 课程媒体表
model CourseMedia {
  id           BigInt      @id @default(autoincrement())
  course_id    BigInt
  media_type   MediaType
  storage_type StorageType @default(LOCAL)
  file_path    String?     @db.VarChar(255)
  file_url     String?     @db.VarChar(255)
  file_blob    Bytes?      @db.LongBlob
  size_bytes   BigInt?
  duration_sec Int?
  checksum     String?     @db.VarChar(64)
  created_at   DateTime    @default(now())

  // 关系
  course Course @relation(fields: [course_id], references: [id])

  // 反向关系
  transcript_sentences TranscriptSentence[]

  @@index([course_id, media_type])
  @@index([storage_type])
  @@map("course_media")
}

enum MediaType {
  VIDEO
  AUDIO
  SLIDE
  IMAGE
}

enum StorageType {
  LOCAL
  DB
}

// 4. 转写句子表
model TranscriptSentence {
  id       BigInt @id @default(autoincrement())
  media_id BigInt
  start_ms Int
  end_ms   Int
  text     String @db.Text

  // 关系
  media CourseMedia @relation(fields: [media_id], references: [id])

  // 反向关系
  annotations Annotation[]

  @@index([media_id])
  @@fulltext([text])
  @@map("transcript_sentence")
}

// 5. 用户标注表
model Annotation {
  id          BigInt         @id @default(autoincrement())
  sentence_id BigInt
  author_id   BigInt
  type        AnnotationType
  payload     Json?
  created_at  DateTime       @default(now())

  // 关系
  sentence TranscriptSentence @relation(fields: [sentence_id], references: [id])
  author   User               @relation(fields: [author_id], references: [id])

  @@index([author_id])
  @@index([sentence_id])
  @@map("annotation")
}

enum AnnotationType {
  HIGHLIGHT
  NOTE
}

// 6. 评估标准主表
model Rubric {
  id          BigInt   @id @default(autoincrement())
  name        String   @unique @db.VarChar(128)
  description String?  @db.Text
  created_at  DateTime @default(now())

  // 反向关系
  rubric_versions RubricVersion[]

  @@index([name])
  @@map("rubric")
}

// 7. 评估标准版本表
model RubricVersion {
  id          BigInt   @id @default(autoincrement())
  rubric_id   BigInt
  version     String   @db.VarChar(32)
  schema      Json
  description String?  @db.Text
  is_active   Boolean  @default(false)
  created_at  DateTime @default(now())

  // 关系
  rubric Rubric @relation(fields: [rubric_id], references: [id])

  // 反向关系
  evaluation_tasks  EvaluationTask[]
  rubric_dimensions RubricDimension[]
  rubric_courses    RubricCourse[]

  @@unique([rubric_id, version])
  @@index([is_active])
  @@map("rubric_version")
}

// 8. 维度表
model RubricDimension {
  id                BigInt  @id @default(autoincrement())
  rubric_version_id BigInt
  name              String  @db.VarChar(64)
  weight            Float?
  max_score         Float?
  description       String? @db.Text

  // 关系
  rubric_version RubricVersion @relation(fields: [rubric_version_id], references: [id])

  // 反向关系
  evaluation_scores EvaluationScore[]

  @@index([rubric_version_id])
  @@map("rubric_dimension")
}

// 9. 评估任务表
model EvaluationTask {
  id                BigInt           @id @default(autoincrement())
  course_id         BigInt
  rubric_version_id BigInt
  evaluator_id      BigInt
  status            EvaluationStatus @default(PENDING)
  created_at        DateTime         @default(now())
  finished_at       DateTime?
  progress          Int              @default(0)
  error_msg         String?          @db.Text
  started_at        DateTime?

  // 关系
  course         Course        @relation(fields: [course_id], references: [id])
  rubric_version RubricVersion @relation(fields: [rubric_version_id], references: [id])
  evaluator      User          @relation(fields: [evaluator_id], references: [id])

  // 反向关系
  evaluation_scores EvaluationScore[]
  evaluation_report EvaluationReport?

  @@index([course_id])
  @@index([evaluator_id])
  @@map("evaluation_task")
}

enum EvaluationStatus {
  PENDING
  RUNNING
  DONE
  FAILED
}

// 10. 维度评分表
model EvaluationScore {
  id           BigInt  @id @default(autoincrement())
  task_id      BigInt
  dimension_id BigInt
  score        Float
  comment      String? @db.Text

  // 关系
  task      EvaluationTask  @relation(fields: [task_id], references: [id])
  dimension RubricDimension @relation(fields: [dimension_id], references: [id])

  @@unique([task_id, dimension_id])
  @@index([task_id])
  @@map("evaluation_score")
}

// 11. 评估报告表
model EvaluationReport {
  id         BigInt   @id @default(autoincrement())
  task_id    BigInt   @unique
  pdf_url    String   @db.VarChar(255)
  created_at DateTime @default(now())

  // 关系
  task EvaluationTask @relation(fields: [task_id], references: [id])

  @@map("evaluation_report")
}

// 12. Rubric 版本 ↔ 课程桥表
model RubricCourse {
  rubric_version_id BigInt
  course_id         BigInt
  assigned_at       DateTime @default(now())

  // 关系
  rubric_version RubricVersion @relation(fields: [rubric_version_id], references: [id])
  course         Course        @relation(fields: [course_id], references: [id])

  @@id([rubric_version_id, course_id])
  @@index([course_id])
  @@map("rubric_course")
}
