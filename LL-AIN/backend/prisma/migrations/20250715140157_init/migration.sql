-- CreateTable
CREATE TABLE `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `username` VA<PERSON>HA<PERSON>(64) NOT NULL,
    `password` VARCHAR(128) NOT NULL,
    `role` ENUM('ADMIN', 'EXPERT', 'TEACHER') NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `user_username_key`(`username`),
    INDEX `user_role_idx`(`role`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `course` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `teacher_id` BIGINT NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `course_teacher_id_idx`(`teacher_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `course_media` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `course_id` BIGINT NOT NULL,
    `media_type` ENUM('VIDEO', 'AUDIO', 'SLIDE', 'IMAGE') NOT NULL,
    `storage_type` ENUM('LOCAL', 'DB') NOT NULL DEFAULT 'LOCAL',
    `file_path` VARCHAR(255) NULL,
    `file_url` VARCHAR(255) NULL,
    `file_blob` LONGBLOB NULL,
    `size_bytes` BIGINT NULL,
    `duration_sec` INTEGER NULL,
    `checksum` VARCHAR(64) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `course_media_course_id_media_type_idx`(`course_id`, `media_type`),
    INDEX `course_media_storage_type_idx`(`storage_type`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `transcript_sentence` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `media_id` BIGINT NOT NULL,
    `start_ms` INTEGER NOT NULL,
    `end_ms` INTEGER NOT NULL,
    `text` TEXT NOT NULL,

    INDEX `transcript_sentence_media_id_idx`(`media_id`),
    FULLTEXT INDEX `transcript_sentence_text_idx`(`text`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `annotation` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `sentence_id` BIGINT NOT NULL,
    `author_id` BIGINT NOT NULL,
    `type` ENUM('HIGHLIGHT', 'NOTE') NOT NULL,
    `payload` JSON NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `annotation_author_id_idx`(`author_id`),
    INDEX `annotation_sentence_id_idx`(`sentence_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `rubric` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(128) NOT NULL,
    `description` TEXT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `rubric_name_key`(`name`),
    INDEX `rubric_name_idx`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `rubric_version` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `rubric_id` BIGINT NOT NULL,
    `version` VARCHAR(32) NOT NULL,
    `schema` JSON NOT NULL,
    `description` TEXT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `rubric_version_is_active_idx`(`is_active`),
    UNIQUE INDEX `rubric_version_rubric_id_version_key`(`rubric_id`, `version`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `rubric_dimension` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `rubric_version_id` BIGINT NOT NULL,
    `name` VARCHAR(64) NOT NULL,
    `weight` DOUBLE NULL,
    `max_score` DOUBLE NULL,
    `description` TEXT NULL,

    INDEX `rubric_dimension_rubric_version_id_idx`(`rubric_version_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `evaluation_task` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `course_id` BIGINT NOT NULL,
    `rubric_version_id` BIGINT NOT NULL,
    `evaluator_id` BIGINT NOT NULL,
    `status` ENUM('PENDING', 'RUNNING', 'DONE', 'FAILED') NOT NULL DEFAULT 'PENDING',
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `finished_at` DATETIME(3) NULL,

    INDEX `evaluation_task_course_id_idx`(`course_id`),
    INDEX `evaluation_task_evaluator_id_idx`(`evaluator_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `evaluation_score` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `task_id` BIGINT NOT NULL,
    `dimension_id` BIGINT NOT NULL,
    `score` DOUBLE NOT NULL,
    `comment` TEXT NULL,

    INDEX `evaluation_score_task_id_idx`(`task_id`),
    UNIQUE INDEX `evaluation_score_task_id_dimension_id_key`(`task_id`, `dimension_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `evaluation_report` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `task_id` BIGINT NOT NULL,
    `pdf_url` VARCHAR(255) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `evaluation_report_task_id_key`(`task_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `rubric_course` (
    `rubric_version_id` BIGINT NOT NULL,
    `course_id` BIGINT NOT NULL,
    `assigned_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `rubric_course_course_id_idx`(`course_id`),
    PRIMARY KEY (`rubric_version_id`, `course_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `course` ADD CONSTRAINT `course_teacher_id_fkey` FOREIGN KEY (`teacher_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `course_media` ADD CONSTRAINT `course_media_course_id_fkey` FOREIGN KEY (`course_id`) REFERENCES `course`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `transcript_sentence` ADD CONSTRAINT `transcript_sentence_media_id_fkey` FOREIGN KEY (`media_id`) REFERENCES `course_media`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `annotation` ADD CONSTRAINT `annotation_sentence_id_fkey` FOREIGN KEY (`sentence_id`) REFERENCES `transcript_sentence`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `annotation` ADD CONSTRAINT `annotation_author_id_fkey` FOREIGN KEY (`author_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `rubric_version` ADD CONSTRAINT `rubric_version_rubric_id_fkey` FOREIGN KEY (`rubric_id`) REFERENCES `rubric`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `rubric_dimension` ADD CONSTRAINT `rubric_dimension_rubric_version_id_fkey` FOREIGN KEY (`rubric_version_id`) REFERENCES `rubric_version`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `evaluation_task` ADD CONSTRAINT `evaluation_task_course_id_fkey` FOREIGN KEY (`course_id`) REFERENCES `course`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `evaluation_task` ADD CONSTRAINT `evaluation_task_rubric_version_id_fkey` FOREIGN KEY (`rubric_version_id`) REFERENCES `rubric_version`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `evaluation_task` ADD CONSTRAINT `evaluation_task_evaluator_id_fkey` FOREIGN KEY (`evaluator_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `evaluation_score` ADD CONSTRAINT `evaluation_score_task_id_fkey` FOREIGN KEY (`task_id`) REFERENCES `evaluation_task`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `evaluation_score` ADD CONSTRAINT `evaluation_score_dimension_id_fkey` FOREIGN KEY (`dimension_id`) REFERENCES `rubric_dimension`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `evaluation_report` ADD CONSTRAINT `evaluation_report_task_id_fkey` FOREIGN KEY (`task_id`) REFERENCES `evaluation_task`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `rubric_course` ADD CONSTRAINT `rubric_course_rubric_version_id_fkey` FOREIGN KEY (`rubric_version_id`) REFERENCES `rubric_version`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `rubric_course` ADD CONSTRAINT `rubric_course_course_id_fkey` FOREIGN KEY (`course_id`) REFERENCES `course`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
